import React from "react";
import styled from "styled-components";
import Label from "../Inputs/Label";
import { translate } from "../../services/language/Translator";
import approvedLeave from "../../assets/images/attendancies/airplane.svg";
import sickLeave from "../../assets/images/attendancies/heart.svg";

interface AbsenceRowsProps {
  usedLeavesThisMonth: number;
  usedHospitalLeavesThisMonth: number;
}

const AbsenceRowsContainer = styled.div`
  display: flex;
  position: absolute;
  flex-direction: column;
  right: 2.5em;
  top: 1em;
`;

const AbsenceRow = styled.div`
  display: flex;
  align-items: left;
  gap: 0.5em;
  max-height: 1em;
  padding: 0 1em;
  margin-bottom: 0.1em;
`;

const AbsenceImage = styled.img`
  width: 0.7em;
  height: 0.7em;
  background-color: #6ac97d;
  border-radius: 50%;
  padding: 0.2em;
`;

const HospitalImage = styled.img`
  width: 0.7em;
  height: 0.7em;
  background-color: #268ddc;
  border-radius: 50%;
  padding: 0.2em;
`;

const AbsenceText = styled(Label)`
  font-size: 0.85rem;
  color: #a8ccf2;
`;

export const AbsenceRows: React.FC<AbsenceRowsProps> = ({
  usedLeavesThisMonth,
  usedHospitalLeavesThisMonth,
}) => {
  return (
    <AbsenceRowsContainer>
      <AbsenceRow>
        <AbsenceImage src={approvedLeave} alt="Approved Leave" />
        <AbsenceText>
          {`${usedLeavesThisMonth} ${translate("Absence")}`}
        </AbsenceText>
      </AbsenceRow>
      <AbsenceRow>
        <HospitalImage src={sickLeave} alt="Sick Leave" />
        <AbsenceText>
          {`${usedHospitalLeavesThisMonth} ${translate("Sick Leave")}`}
        </AbsenceText>
      </AbsenceRow>
    </AbsenceRowsContainer>
  );
};
