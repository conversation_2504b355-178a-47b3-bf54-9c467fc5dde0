﻿using Gateway.Common.Globals;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Enums;
using HR = WorkTimeApi.Common.Requests.Companies;
using UR = Microinvest.TransferFiles.Tools.Models.Companies;
using WorktimeUserDTO = WorkTimeApi.Common.DTOs.Users.UserDTO;

namespace Gateway.Handlers.Companies
{
    public class AddEmployeeCompanyHandler(IWorkTimeApiConnection workTimeApiConnection,
        IUserRegistrationConnection userRegistrationConnection,
        GlobalUser globalUser) : IRequestHandler<AddEmployeeCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(AddEmployeeCompanyRequest request, CancellationToken cancellationToken)
        {
            var companyRequest = new HR.CreateCompanyRequest()
            {
                Name = request.Name,
                Bulstat = request.Bulstat,
                ContactName = request.ContactName,
                UserRegistrationCompanyId = request.UserRegistrationCompanyId
            };

            var createCompanyResponse = await workTimeApiConnection.CreateCompanyAsync(companyRequest);
            if (!createCompanyResponse.IsSuccessStatusCode)
                return new HttpResponseMessageResult(createCompanyResponse);

            await userRegistrationConnection.EditCompanyAsync(new UR.CompanyDTO
            {
                Id = request.UserRegistrationCompanyId,
                Name = request.Name,
                Bulstat = request.Bulstat,
                ContactName = request.ContactName,
            });

            var companyDTO = await createCompanyResponse.Content.ReadFromJsonAsync<CompanyDTO>(cancellationToken);
            if (companyDTO is null)
                return Results.NoContent();

            var getCompanyUsersResponse = await userRegistrationConnection.GetSenderaCompanyUsersAsync(globalUser.Email, request.Bulstat);
            if (!getCompanyUsersResponse.IsSuccessStatusCode)
                return Results.StatusCode((int)getCompanyUsersResponse.StatusCode);

            var companyUsersDTO = await getCompanyUsersResponse.Content.ReadFromJsonAsync<List<UserDTO>>();
            if (companyUsersDTO is null)
                return Results.NoContent();

            foreach (var companyUserDTO in companyUsersDTO.Where(u => u.Email != globalUser.Email))
            {
                var user = new WorktimeUserDTO
                {
                    Email = companyUserDTO.Email,
                    FirstName = companyUserDTO.FirstName,
                    SecondName = companyUserDTO.SecondName,
                    LastName = companyUserDTO.LastName,
                    Id = new Guid(companyUserDTO.Id)
                };
                var addUserWithEmployeeRequest = new WorkTimeApi.Common.Requests.Users.AddUserWithEmployeeRequest
                {
                    User = user,
                    CompanyId = companyDTO.Id,
                    Status = EmployeeStatus.Active
                };

                var addUserResponse = await workTimeApiConnection.AddUserWithEmployeeAsync(addUserWithEmployeeRequest);
                if (!addUserResponse.IsSuccessStatusCode)
                    return Results.StatusCode((int)addUserResponse.StatusCode);

                var userDTO = await addUserResponse.Content.ReadFromJsonAsync<EmployeeDTO>(cancellationToken);
                if (userDTO is null)
                    return Results.NoContent();

                if (userDTO.Email is not null)
                {
                    await workTimeApiConnection.ShareCompanyAsync(new HR.ShareCompanyRequest
                    {
                        CompanyId = companyDTO.Id,
                        Email = userDTO.Email,
                        RoleId = new Guid(DefaultWorktimeRole.Employee),
                        RoleName = ""
                    });
                }
            }

            return Results.Ok(companyDTO);
        }
    }
}
