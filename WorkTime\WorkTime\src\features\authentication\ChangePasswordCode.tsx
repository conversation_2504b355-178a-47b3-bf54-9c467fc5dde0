import { useNavigate } from "react-router-dom";
import MainWindowContainer from "../../components/MainWindowContainer";
import { ChangeEvent, MouseEvent, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";
import PasswordBox from "../../components/Inputs/PasswordBox";
import Button from "../../components/Inputs/Button";
import { PasswordStrengthType } from "../../models/Enums/PasswortStrengthType";
import styled from "styled-components";
import { passwordStrengthCheck } from "../../services/authentication/passwordService";
import mainAnimation from "../../assets/images/main-animation.svg";
import { authenticatedPost } from "../../services/connectionService";
import { LOCAL_STORAGE_HAS_SIGNED_IN } from "../../constants/local-storage-constants";
import { translate } from "../../services/language/Translator";
import { toast } from "react-toastify";
import { TermsAndConditions } from "./TermsAndConditions";
import Checkbox from "../../components/Inputs/Checkbox";
import { ChangePasswordRequest } from "../../models/Requests/ChangePasswordRequest";
import { useModal } from "../../components/PopUp/ActionModalContext";
import { PasswordStrengthIndicator } from "./PasswordStrengthIndicator";
import Label from "../../components/Inputs/Label";

const AcceptTermsContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
`;

const ChangePasswordContainer = styled(MainWindowContainer)`
  height: 100%;
  margin: 0 auto;
  width: clamp(40%, 30rem, 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const PasswordStrengthIndicatorContainer = styled.div`
  color: var(--password-strength-indicator-color);
  margin-top: 0.5rem;
  text-align: center;
  min-height: 1.5em;
`;

const StyledButton = styled(Button)`
  width: -webkit-fill-available;
  position: "relative";
  bottom: 0;
  margin: 0.625rem;
`;

const TopLeftMessage = styled.p`
  padding: 2rem;
  font-size: 1.1rem;
  width: 40%;
  text-align: left;
  line-height: 1.5;
  resize: none;
  overflow: hidden;
`;

const ChangePasswordCode = () => {
  const { user, setUser } = useAuth();
  const [password, setPassword] = useState("");
  const [oldPassword, setOldPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [enabled, setEnabled] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [showStrengthHint, setShowStrengthHint] = useState(false);
  const { openModal } = useModal();
  const navigate = useNavigate();
  const [passwordStrength, setPasswordStrength] = useState(
    PasswordStrengthType.Empty
  );

  useEffect(() => {
    setEnabled(
      acceptTerms &&
        password === confirmPassword &&
        password !== "" &&
        passwordStrength >= PasswordStrengthType.Strong
    );
  }, [password, confirmPassword, passwordStrength, acceptTerms]);

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);

    const currentPasswordStrength = passwordStrengthCheck(newPassword);
    setPasswordStrength(currentPasswordStrength);
  };

  const handleConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.currentTarget.value);
  };

  const handleoldPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setOldPassword(e.currentTarget.value);
  };
  const handleChangePasswordClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const request = {
      oldPassword: oldPassword,
      password: password,
    } as ChangePasswordRequest;

    authenticatedPost("sso/change-password", request)
      .then(() => {
        localStorage.setItem(LOCAL_STORAGE_HAS_SIGNED_IN, "true");
        setUser({ ...user, hasSignedIn: true });
        openModal({
          type: "info",
          title: "Password changed successfully",
          requireMessage: false,
          confirmLabel: "Ok",
          cancelLabel: "",
          onConfirm: async () => {
            navigate("/");
          },
        });
        setOldPassword("");
        setPassword("");
        setConfirmPassword("");
      })
      .catch((error) => {
        console.log(error);
        const errorMessage =
          error === 404
            ? translate("Incorrect password")
            : translate("BadRequestError");

        toast.error(errorMessage, {
          position: "top-right",
          autoClose: 10000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      });
  };

  const handleTermsCheckboxClick = () => {
    setAcceptTerms(!acceptTerms);
  };

  const confirmPasswordValidation = {
    isValid:
      confirmPassword === undefined ||
      confirmPassword === "" ||
      password === undefined ||
      password === "" ||
      confirmPassword === password,
    alertMessage: translate("Passwords does not match"),
  };
  return (
    <div>
      <TopLeftMessage>
        <Label>Welcome!</Label>
        <br />
        <Label>strChangePasswordMessage</Label>
      </TopLeftMessage>
      <ChangePasswordContainer data-testid="change-password-container">
        <PasswordBox
          name="oldPassword"
          handleChange={handleoldPasswordChange}
          label="Old Password"
          type="password"
          value={oldPassword}
          data-testid="password-box"
        />
        <PasswordBox
          name="password"
          handleChange={handlePasswordChange}
          label="New password"
          type="password"
          value={password}
          data-testid="password-box"
        />
        <PasswordBox
          name="confirm-password"
          handleChange={handleConfirmPasswordChange}
          label="Repeat password"
          type="password"
          value={confirmPassword}
          data-testid="confirm-password-box"
          validation={confirmPasswordValidation}
        />

        <StyledButton
          label="Change password"
          disabled={!enabled}
          onClick={handleChangePasswordClick}
          data-testid="change-password-button"
        />
        <div
          onMouseEnter={() => setShowStrengthHint(true)}
          onMouseLeave={() => setShowStrengthHint(false)}
        >
          <PasswordStrengthIndicator
            passwordStrength={passwordStrength}
            setShowStrengthHint={() => {}}
          />
          <PasswordStrengthIndicatorContainer
            style={{ visibility: showStrengthHint ? "visible" : "hidden" }}
          >
            {translate("strPasswordStrenghtIndicator")}
          </PasswordStrengthIndicatorContainer>
        </div>

        <AcceptTermsContainer data-testid="accept-terms-container">
          <Checkbox
            isChecked={acceptTerms}
            handleChange={handleTermsCheckboxClick}
            label=""
            name="acceptTerms"
          />
          <TermsAndConditions headerTitle="General rules and privacy policy" />
        </AcceptTermsContainer>
      </ChangePasswordContainer>
      <img
        src={mainAnimation}
        alt="Main Animation"
        style={{
          position: "fixed",
          right: "0",
          bottom: "0",
          pointerEvents: "none",
        }}
      />
    </div>
  );
};

export default ChangePasswordCode;
