{"Template": "Пример", "Test Home Page": "Хоум страница за тест", "Home": "Начало", "Password": "Парола", "Confirm Password": "Потвърдете паролата", "Registration": "Регистрация", "Login": "Вход", "strCreatePayroll": "Ново назначение", "strWrongCredentials": "Невалидни потребителско име или парола", "strLogout": "Излизане от профила", "strContinueWith": "Продължи с:", "Delete": "Изтрий", "AddNew": "Добавяне на нов", "strShowPayrolls": "Назначения", "Sign In with Google account": "Влез с Google акаунт", "Sign In with Facebook account": "Влез с Facebook акаунт", "Sign In with Microsoft account": "Влез с Microsoft акаунт", "Person data": "Досие", "Employees": "Служители", "Contracts": "Договори", "Structure": "Структура", "strJan": "<PERSON><PERSON><PERSON>", "strFeb": "<PERSON><PERSON><PERSON>", "strMar": "<PERSON><PERSON><PERSON>", "strApr": "<PERSON><PERSON><PERSON>", "strMay": "<PERSON><PERSON><PERSON>", "strJun": "Юни", "strJul": "<PERSON><PERSON><PERSON>", "strAug": "Авг", "strSep": "Сеп", "strOct": "Окт", "strNov": "Ное", "strDec": "<PERSON><PERSON><PERSON>", "strMon": "Пон", "strTue": "Вт", "strWed": "Ср", "strThu": "Чет", "strFri": "Пет", "strSat": "Съб", "strSun": "Нед", "strGetInputDate": "Дата", "Forgotten password?": "Забравена парола?", "Remember me": "Запомни ме", "Accept Terms": "Съгласявам се с условията", "General rules and privacy policy": "Общи правила и политика за поверителност", "Name": "Име", "Last name": "Фамилия", "Company Name": "Име на фирмата", "MOL": "МОЛ", "Create Company": "Създай фирма", "Welcome Message": "Добре дошли! Успешно се регистрирахте в системата на Microinvest Work Time!", "EmployeesTable": "служителя", "Previous": "< ..", "Next": ".. >", "Reset password": "Възстановяване на парола", "Email sent successfully!": "E-mail за смяна на парола е изпратен успешно!", "EIK:": "ЕИК:", "MOL:": "МОЛ:", "My companies": "Моите фирми", "Tasks": "Зада<PERSON>и", "Add company": "Добавяне на фирма", "Join company": "Присъединяване", "Accept": "Приеми", "SuccessfullJoinRequest": "Успешно е изпратено запитване за присъединяване към", "Ask": "Запитване", "Import": "Импорт", "Add Employee": "Добавяне на служител", "EmployeeSuccessfullyCreated": "Служителят беше добавен успешно!", "Second name": "Презиме", "Invalid Email": "Нев<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Company {0} was successfully added": "Фирма {0} беше импортирана успешно", "Company logo": "Фирмено лого", "Payrolls": "Назначения", "LandingPageText1": "Възможно ли е времето да бъде наш съюзник?", "LandingPageText2": "Планирайте умно за по-добри резултати!", "LandingPageText3": "Защо не тук и сега?", "Create": "Създаване", "Edit": "Редакция", "Editing": "Редакция", "Sharing": "Споделяне", "Invite": "Покани", "Absence request": "Заявление за отсъствие", "Payroll for": "Договор за", "10000": "Компанията вече е добавена за този потребител", "10001": "Компанията вече съществува и не може да бъде създадена", "30000": "Невалиден отпуск", "30001": "Въведеният отпуск се припокрива с един или повече отпуски. Моля, коригирайте периода на отсъствието.", "30002": "Въведеният отпуск се припокрива с един или повече болнични. Моля, коригирайте периода на отсъствието.", "30003": "Въведеният болничен се припокрива с един или повече отпуски. Периодите на отпуск ще бъдат коригирани след потвърждение на текущото отсъствие от оторизиран потребител.", "30004": "Не е открито назначение за този служител.", "30005": "Отпускът е преминал и не може да бъде коригиран.", "30006": "Отпускът е започнал. Заявка за потвърждение е изпратена на администратора.", "Personal Data": "Лични данни", "Profile": "Профил", "First Name": "Име", "Surname": "Презиме", "Family Name": "Фамилия", "Personal Identification Number": "ЕГН", "Date of Birth": "Дата на раждане", "Place of Birth": "Място на раждане", "Personal Identity Card": "<PERSON><PERSON><PERSON><PERSON> карта", "Information from the personal identity card": "Информация по лична карта", "Appointment": "Назначение", "Events": "Отсъствия", "Attendancies": "Присъствия", "Hospitals": "Болнични", "Documents": "Документи", "ID card number": "Номер на лична карта", "Issued on": "Издадена на", "Address ": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Contact Information": "Данни за коренспонденция", "Number of minor children": "Брой деца под 18 годишна възраст", "Personal Information": "Лични данни", "Man": "Мъж", "Woman": "Жена", "Gender": "Пол", "Save": "Запази", "Cancel": "Отказ", "Place": "<PERSON>р<PERSON><PERSON>", "Street": "Улица", "Date": "Дата", "PermanentContractType_Main": "Основен", "PermanentContractType_Additional": "Допълнителен", "AD": "Андора", "AE": "Обединени арабски емирства", "AF": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AL": "Албания", "AM": "Армения", "AO": "Ангола", "AR": "Аржентина", "AT": "Австрия", "AU": "Австралия", "AZ": "Азерб<PERSON>йджан", "BA": "Босна и Херцеговина", "BB": "Б<PERSON>рб<PERSON>д<PERSON>с", "BD": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BE": "Белгия", "BG": "България", "BH": "Б<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BI": "Бурунди", "BJ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BO": "Боливия", "BR": "Бразилия", "BY": "<PERSON>е<PERSON><PERSON><PERSON><PERSON><PERSON>", "CA": "Канада", "CG": "Конго", "CH": "Швейцария", "CI": "Кот д'Ивоар", "CL": "Чили", "CM": "Камер<PERSON>н", "CN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CO": "Колумбия", "CR": "Коста Рика", "CU": "Куба", "CY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CZ": "Чехия", "DE": "Германия", "DK": "Дания", "DO": "Доминиканска република", "DZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EC": "Еквадор", "EE": "Естония", "EG": "Египет", "ES": "Испания", "ET": "Етиопия", "FI": "Финландия", "FR": "Франция", "GA": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GB": "Обединеното кралство", "GD": "Гренада", "GE": "Грузия", "GH": "<PERSON><PERSON><PERSON>", "GI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GM": "Гамбия", "GN": "Гвинея", "GR": "Гърция", "GS": "Южна Джорджия и Южни Сандвичеви острови", "GT": "Гватемала", "HK": "Хонконг", "HN": "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HR": "Хърватия", "HT": "<PERSON><PERSON><PERSON><PERSON>и", "HU": "Унгария", "ID": "Индонезия", "IE": "Ирландия", "IL": "Изра<PERSON>л", "IN": "Индия", "IQ": "Ирак", "IR": "<PERSON>р<PERSON>н", "IS": "Исландия", "IT": "Италия", "JM": "Ямайка", "JO": "Йордания", "JP": "Япония", "KE": "Кения", "KG": "Киргизстан", "KH": "Камбоджа", "KN": "Сейнт Китс и Невис", "KP": "Северна Корея", "KR": "Южна Корея", "KW": "<PERSON>у<PERSON><PERSON><PERSON><PERSON>", "KZ": "Казахстан", "LA": "Ла<PERSON>с", "LB": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LI": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "LK": "Шри Ланка", "LR": "Либерия", "LS": "Лесото", "LT": "Литва", "LU": "Люксембург", "LV": "Латвия", "LY": "Либия", "MA": "Мароко", "MD": "Молдова", "ME": "Черна гора", "MG": "Мадагаскар", "MK": "Македония", "ML": "Мали", "MM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Бирма)", "MN": "Монголия", "MR": "Мавритания", "MT": "Малта", "MX": "Мексико", "MY": "Малайзия", "MZ": "Мозамбик", "NA": "Намибия", "NE": "<PERSON>и<PERSON><PERSON><PERSON>", "NG": "Нигерия", "NI": "Никарагуа", "NL": "Нидерландия", "NO": "Норвегия", "NP": "Непал", "NZ": "Нова Зеландия", "OM": "О<PERSON><PERSON><PERSON>", "PA": "Панама", "PE": "<PERSON>ер<PERSON>", "PH": "Филипини", "PK": "Пакистан", "PL": "Полша", "PS": "Палестински територии", "PT": "Португалия", "PY": "Параг<PERSON><PERSON>й", "QA": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RO": "Румъния", "RU": "Русия", "RW": "Руанда", "SA": "Саудитска Арабия", "SD": "Судан", "SE": "Швеция", "SG": "Синга<PERSON>ур", "SI": "Словения", "SK": "Словакия", "SM": "Сан Марино", "SN": "Сенегал", "SO": "Сомалия", "SR": "Су<PERSON><PERSON><PERSON><PERSON>", "SV": "Салвадор", "SY": "Сирия", "SZ": "Свазиленд", "TD": "Чад", "TG": "Того", "TH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TJ": "Тад<PERSON><PERSON><PERSON>истан", "TL": "Източен Тимор", "TM": "Туркменистан", "TN": "<PERSON>у<PERSON><PERSON><PERSON>", "TR": "Турция", "TT": "Тринидад и Тобаго", "TW": "Тайван", "TZ": "Танзания", "UA": "Украйна", "UG": "Уганда", "UM": "Малки отдалечени острови на САЩ", "US": "Съединени щати", "UY": "Уругвай", "UZ": "Узбекистан", "VA": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>", "VE": "Венецуела", "VG": "Британски Вирджински острови", "VN": "Вие<PERSON><PERSON><PERSON>", "RS": "Сърбия", "YE": "Йемен", "YT": "Майот", "ZA": "Южна Африка", "ZM": "Замбия", "ZW": "Зимбабве", "EH": "Западна Сахара", "MC": "Монако", "PR": "Пуерто Рико", "ZR": "<PERSON><PERSON><PERSON><PERSON>", "XK": "Косово", "AN": "Нидерландски Антили", "FX": "Метрополна Франция", "GQ": "Екваториална Гвинея", "KY": "Кайманови острови", "IM": "О<PERSON><PERSON>ров Ман", "TerminationReason_Не_е_избрано": "Не е избрано", "TerminationReason_чл71ал1КТ": "чл. 71, ал. 1 КТ", "TerminationReason_чл325ал1т1": "чл. 325, ал. 1, т. 1 КТ", "TerminationReason_чл325ал1т2": "чл. 325, ал. 1, т. 2 КТ", "TerminationReason_чл325ал1т3": "чл. 325, ал. 1, т. 3 КТ", "TerminationReason_чл325ал1т4": "чл. 325, ал. 1, т. 4 КТ", "TerminationReason_чл325ал1т5": "чл. 325, ал. 1, т. 5 КТ", "TerminationReason_чл325ал1т6": "чл. 325, ал. 1, т. 6 КТ", "TerminationReason_чл325ал1т8": "чл. 325, ал. 1, т. 8 КТ", "TerminationReason_чл325ал1т9": "чл. 325, ал. 1, т. 9 КТ", "TerminationReason_чл325ал1т10": "чл. 325, ал. 1, т. 10 КТ", "TerminationReason_чл325ал1т11": "чл. 325, ал. 1, т. 11 КТ", "TerminationReason_чл325ал1т12": "чл. 325, ал. 1, т. 12 КТ", "TerminationReason_чл325ал2": "чл. 325, ал. 2 КТ", "TerminationReason_чл326ал1": "чл. 326, ал. 1 КТ", "TerminationReason_чл327ал1т1": "чл. 327, ал. 1, т. 1 КТ", "TerminationReason_чл327ал1т2": "чл. 327, ал. 1, т. 2 КТ", "TerminationReason_чл327ал1т3": "чл. 327, ал. 1, т. 3 КТ", "TerminationReason_чл327ал1т3а": "чл. 327, ал. 1, т. 3а КТ", "TerminationReason_чл327ал1т4": "чл. 327, ал. 1, т. 4 КТ", "TerminationReason_чл327ал1т6": "чл. 327, ал. 1, т. 6 КТ", "TerminationReason_чл327ал1т7": "чл. 327, ал. 1, т. 7 КТ", "TerminationReason_чл327ал1т7а": "чл. 327, ал. 1, т. 7а КТ", "TerminationReason_чл327ал1т8": "чл. 327, ал. 1, т. 8 КТ", "TerminationReason_чл327ал1т9": "чл. 327, ал. 1, т. 9 КТ", "TerminationReason_чл327ал1т10": "чл. 327, ал. 1, т. 10 КТ", "TerminationReason_чл327ал1т11": "чл. 327, ал. 1, т. 11 КТ", "TerminationReason_чл327ал1т12": "чл. 327, ал. 1, т. 12 КТ", "TerminationReason_чл328ал1т1": "чл. 328, ал. 1, т. 1 КТ", "TerminationReason_чл328ал1т2": "чл. 328, ал. 1, т. 2 КТ", "TerminationReason_чл328ал1т3": "чл. 328, ал. 1, т. 3 КТ", "TerminationReason_чл328ал1т4": "чл. 328, ал. 1, т. 4 КТ", "TerminationReason_чл328ал1т5": "чл. 328, ал. 1, т. 5 КТ", "TerminationReason_чл328ал1т6": "чл. 328, ал. 1, т. 6 КТ", "TerminationReason_чл328ал1т7": "чл. 328, ал. 1, т. 7 КТ", "TerminationReason_чл328ал1т8": "чл. 328, ал. 1, т. 8 КТ", "TerminationReason_чл328ал1т10": "чл. 328, ал. 1, т. 9 КТ", "TerminationReason_чл328ал1т10а": "чл. 328, ал. 1, т. 10а КТ", "TerminationReason_чл328ал1т10б": "чл. 328, ал. 1, т. 10б КТ", "TerminationReason_чл328ал1т10в": "чл. 328, ал. 1, т. 10в КТ", "TerminationReason_чл328ал1т11": "чл. 328, ал. 1, т. 11 КТ", "TerminationReason_чл328ал1т12": "чл. 328, ал. 2 КТ", "TerminationReason_чл328ал2": "чл. 328, ал. 1, т. 12 КТ", "TerminationReason_чл330ал1": "чл. 330, ал. 1 КТ", "TerminationReason_чл330ал2т1": "чл. 330, ал. 2, т. 1 КТ", "TerminationReason_чл330ал2т2": "чл. 330, ал. 2, т. 2 КТ", "TerminationReason_чл330ал2т3": "чл. 330, ал. 2, т. 3 КТ", "TerminationReason_чл330ал2т5": "чл. 330, ал. 2, т. 5 КТ", "TerminationReason_чл330ал2т6": "чл. 330, ал. 2, т. 6 КТ", "TerminationReason_чл330ал2т7": "чл. 330, ал. 2, т. 7 КТ", "TerminationReason_чл330ал2т8": "чл. 330, ал. 2, т. 8 КТ", "TerminationReason_чл330ал2т9": "чл. 330, ал. 2, т. 9 КТ", "TerminationReason_чл330ал2т10": "чл. 330, ал. 2, т. 10 КТ", "TerminationReason_чл330ал2т11": "чл. 330, ал. 2, т. 11 КТ", "TerminationReason_чл331": "чл. 331 КТ", "TerminationReason_чл334ал1": "чл. 334, ал. 1 КТ", "TerminationReason_чл337": "чл. 337 КТ", "TerminationReason_чл338": "чл. 338 КТ", "TerminationReason_чл19аал2ЗА": "чл. 19а, ал. 2 от Закона за администрацията", "TerminationReason_чл19аал3ЗА": "чл. 19а, ал. 3 от Закона за администрацията", "TerminationReason_чл340аал5т1ЗСВ": "чл. 340а, ал. 5, т. 1 от Закона за съдебната власт", "TerminationReason_чл340аал5т2ЗСВ": "чл. 340а, ал. 5, т. 2 от Закона за съдебната власт", "TerminationReason_чл340аал5т3ЗСВ": "чл. 340а, ал. 5, т. 3 от Закона за съдебната власт", "TerminationReason_чл340аал6ЗСВ": "чл. 340а, ал. 6 от Закона за съдебната власт", "TerminationReason_друго": "друго", "IncomeType_301": "Доходи от дейност като регистриран земеделски стопанин за производството на непреработени продукти от селско стопанство, с изключение на доходите от производството на декоративна растителност, по чл.29, ал.1, т.1 от ЗДДФЛ", "IncomeType_302": "Доходи за производство на преработени или непреработени продукти от селско стопанство, извън тези с код 301, по чл. 29, ал. 1, т. 2, б. \"а\" от ЗДДФЛ", "IncomeType_303": "Доходи от горско стопанство (включително от събиране на диворастящи билки, гъби и плодове), от ловно стопанство и от рибно стопанство по чл. 29, ал. 1, т. 2, б. \"а\" от ЗДДФЛ", "IncomeType_304": "Авторски и лицензионни възнаграждения, включително за доходи от продажба на изобретения, произведения на науката, културата и изкуството от техните автори, както и възнаграждения за изпълнения на артисти-изпълнители по чл. 29, ал. 1, т. 2, б. \"б\" от ЗДДФЛ", "IncomeType_305": "Доходи от упражняване на занаят, които не се облагат с патентен данък по реда на Закона за местните данъци и такси, по чл. 29, ал. 1, т. 2, б. \"в\" от ЗДДФЛ", "IncomeType_306": "Доходи от упражняване на свободна професия по чл. 29, ал. 1, т. 3 от ЗДДФЛ", "IncomeType_3061": "Доходи от упражняване на свободна професия като адвокат", "IncomeType_3071": "Финансови и застрахователни посреднически услуги", "IncomeType_30710": "Услуги за превоз на товари", "IncomeType_30711": "Строителни услуги и услуги по ремонт, поддържане и реконструкции на сгради", "IncomeType_30712": "Услуги по ремонт и поддържане на превозни средства", "IncomeType_30713": "Услуги по изработка на облекло и изделия от кожа", "IncomeType_30714": "Други извънтрудови правоотношения", "IncomeType_3072": "Посреднически услуги и дистрибуция на стоки", "IncomeType_3073": "Консултантски услуги в различни области", "IncomeType_3074": "Счетоводни и юридически услуги", "IncomeType_3075": "Услуги по хуманно здравеопазване и ветеринарномедицински дейности", "IncomeType_3076": "Услуги в областта на информационните технологии", "IncomeType_3077": "Архитектурни и инженерни услуги", "IncomeType_3078": "Услуги по образование и обучение в областта на спорта, активния отдих и изкуствата", "IncomeType_3079": "Услуги за превоз на пътници", "IncomeType_401": "Доходи от наем на недвижимо имущество, включително придобити вноски по договор за лизинг, в който не е предвидено прехвърляне правото на собственост на недвижимо имущество по чл. 31 от ЗДДФЛ", "IncomeType_402": "Доходи от наем на движимо имущество, включително придобити вноски по договор за лизинг, в който не е предвидено прехвърляне правото на собственост на движимо имущество по чл. 31 от ЗДДФЛ", "IncomeType_403": "Възнаграждения по договори за: франча<PERSON><PERSON>, факторинг и други договори за предоставяне за ползване на права по чл. 31 от ЗДДФЛ", "IncomeType_501": "Доходи от продажба или замяна на недвижимо имущество, включително ограничени вещни права върху такова имущество", "IncomeType_502": "Доходи от продажба или замяна на недвижимо имущество, включително ограничени вещни права върху такова имущество, когато продажната цена се плаща на части в различни данъчни години", "IncomeType_503": "Доходи от продажба или замяна на движимо имущество (в т.ч. пътни, въздухоплавателни и водни превозни средства; произведения на изкуството, предмети за колекции и антикварни предмети и др. извън посочените с код 504 - 509)", "IncomeType_504": "Доходи от продажба или замяна на движимо имущество, когато продажната цена се плаща на части в различни данъчни години", "IncomeType_505": "Доходи от предоставяне на имущество по договор за лизинг, в който изрично е предвидено прехвърляне на правото на собственост върху имуществото", "IncomeType_506": "Доходи от продажба на вещи съгласно чл. 27, ал. 5 от ЗДДФЛ", "IncomeType_507": "Доход от прехвърляне на предприятие на едноличен търговец със заличаване", "IncomeType_508": "Доходи от продажба или замяна на акции, дялове, компенсаторни инструменти, инвестиционни бонове и други финансови активи, с изключение на тези по чл. 13, ал. 1, т. 3 от ЗДДФЛ", "IncomeType_509": "Доходи от търговия с чуждестранна валута", "IncomeType_601": "Обезщетения за пропуснати ползи и неустойки с такъв характер по чл. 35, т. 1 от ЗДДФЛ", "IncomeType_603": "Лихви, в т. ч. съдържащи се във вноски по лизинг, по чл. 35, т. 3 от ЗДДФЛ", "IncomeType_604": "Производствени дивиденти от кооперации по чл. 35, т. 4 от ЗДДФЛ", "IncomeType_605": "Упражняване на права на интелектуална собственост по наследство по чл. 35, т. 5 от ЗДДФЛ", "IncomeType_606": "Доходи от други източници, извън посочените в предходните кодове, по чл. 35, т. 6 от ЗДДФЛ", "IncomeType_801": "Обезщетения за пропуснати ползи и неустойки с такъв характер по чл. 37, ал. 1, т. 1 от ЗДДФЛ", "IncomeType_802": "Стипендии за обучение в страната и чужбина по чл. 37, ал. 1, т. 2 от ЗДДФЛ", "IncomeType_803": "Лихви, в т. ч. съдържащи се във вноски по лизинг, с изключение на лихви по облигации или други дългови ценни книжа, издадени от държавата или общините и допуснати до търговия на регулиран пазар в страната или в държава - членка на ЕС, или в друга държава - страна по Споразумението за ЕИП - чл. 37, ал. 1, т. 3 от ЗДДФЛ", "IncomeType_804": "Доходи от наем или от друго възмездно предоставяне за ползване на движимо или недвижимо имущество, включително вноски по договор за лизинг, в който не е изрично предвидено прехвърляне на правото на собственост, по чл. 37, ал. 1, т. 4 от ЗДДФЛ", "IncomeType_805": "Възнаграждения по договори за франчайз и факторинг по чл. 37, ал. 1, т. 5 от ЗДДФЛ", "IncomeType_806": "Авторски и лицензионни възнаграждения по чл. 37, ал. 1, т. 6 от ЗДДФЛ", "IncomeType_807": "Възнаграждения за технически услуги по чл. 37, ал. 1, т. 7 от ЗДДФЛ", "IncomeType_808": "Награди и възнаграждения за дейност, извършена на територията на страната от чуждестранни физически лица - общественици, дейци на науката, изкуството, културата и спорта, включително когато доходът е изплатен/начислен чрез трето лице (импресарска агенция, продуцентска къща и други посредници) по чл. 37, ал. 1, т. 8 от ЗДДФЛ", "IncomeType_809": "Доходи от управление и контрол, от участие в управителни и контролни органи на предприятия по чл. 37, ал. 1, т. 9 от ЗДДФЛ", "IncomeType_810": "Доходи от продажба, замяна и друго възмездно прехвърляне на недвижимо имущество по чл. 37, ал. 1, т. 10 от ЗДДФЛ", "IncomeType_811": "Вноски по договор за лизинг, в който изрично е предвидено прехвърляне на правото на собственост върху недвижимо имущество по чл. 37, ал. 1, т. 11 от ЗДДФЛ", "IncomeType_812": "Доходи от продажба, замяна и друго възмездно прехвърляне на акции, дялове, компенсаторни инструменти, инвестиционни бонове и други финансови активи по чл. 37, ал. 1, т. 12 от ЗДДФЛ", "IncomeType_813": "Неустойки и обезщетения от всякакъв вид, с изключение на обезщетенията по застрахователни договори, начислени от местни юридически лица, местни еднолични търговци или чуждестранни юридически лица и еднолични търговци чрез място на стопанска дейност или определена база в страната в полза на чуждестранни физически лица, установени в юрисдикции с преференциален данъчен режим – чл. 37, ал. 1 във връзка с чл. 8, ал. 11 от ЗДДФЛ", "IncomeType_814": "Доходи от дивиденти и ликвидационни дялове в полза на физически лица по чл. 38, ал. 1 от ЗДДФЛ", "IncomeType_8141": "Общ размер на доходите от дивиденти по чл. 38, ал. 1 от ЗДДФЛ с годишен размер до 100лв. на физическо лице, извън посочените с код 814", "IncomeType_815": "Доходи придобити от замяна на акции и дялове във връзка с преобразуване на дружества по глава деветнадесета, раздел ІІ от ЗКПО по чл. 38, ал. 5 от ЗДДФЛ", "IncomeType_816": "Доходи от допълнително доброволно осигуряване по чл. 38, ал. 8 от ЗДДФЛ", "IncomeType_817": "Доходи от доброволно здравно осигуряване и от застраховки 'Живот' по чл. 38, ал. 8 от ЗДДФЛ", "IncomeType_818": "Доходи от продажба или замяна на движимо имущество, предадено на лица, които имат право да извършват събиране, транспортиране, оползотворяване или обезвреждане на отпадъци в съответствие със Закона за управление на отпадъците по чл. 38, ал. 10 от ЗДДФЛ", "IncomeType_819": "Доходи от лихви, придобити от член на кооперация по заеми към кооперацията по чл. 38, ал. 11 от ЗДДФЛ", "IncomeType_820": "Доходи от наем или друго възмездно предоставяне на имущество в режим на етажна собственост с форма на управление общо събрание на собствениците по чл. 38, ал. 12 от ЗДДФЛ", "IncomeType_821": "Доходи от лихви по банкови сметки на местни физически лица по чл. 38, ал. 13 от ЗДДФЛ", "IncomeType_822": "Облагаеми парични и предметни награди от игри, от състезания и конкурси, които не са предоставени от работодател или възложител", "IncomeType_901": "Доходи от разпореждане с финансови инструменти по смисъла на § 1, т. 11 от ДР на ЗДДФЛ (чл. 13, ал. 1, т. 3 от ЗДДФЛ)", "IncomeType_902": "Доходи от допълнително доброволно осигуряване, получени след придобиване право на допълнителна пенсия; доходи от инвестиции на техническите резерви, получени по застрахователни договори; доходи от инвестиции на активите на фондовете за допълнително пенсионно осигуряване, разпределени по индивидуалните партиди на осигурените лица (чл. 13, ал. 1, т. 7 от ЗДДФЛ)", "IncomeType_904": "Лихви и отстъпки от български държавни, общински и корпоративни облигации, както и от подобни облигации, емитирани съгласно законодателството на друга държава-членка на Европейския съюз, или на държава - страна по Споразумението за  Европейското икономическо пространство (чл.13, ал.1, т. 9 от ЗДДФЛ)", "IncomeType_905": "Парични и предметни печалби, получени от участие в хазартни игри, организирани с лиценз, издаден по реда на Закона за хазарта или съгласно законодателството на друга държава - членка на Европейския съюз, или държава - страна по Споразумението за Европейското икономическо пространство (чл. 13, ал. 1, т. 20 от ЗДДФЛ)", "IncomeType_907": "Доходи, получени от рента, аренда или от друго възмездно предоставяне за ползване на земеделска земя (чл. 13, ал. 1, т. 24 от ЗДДФЛ)", "Other": "Други", "Remaining": "Остав<PERSON>щ", "Paid annual leave": "Платен годишен отпуск", "Paid annual": "Платен годишен", "leave": "отпуск", "Used": "Използван", "Sick Leave": "Болничен", "days": "дни", "day": "ден", "in": "за", "Confirm": "Заяви Отсъствие", "Monday": "Понеделник", "Tuesday": "Вторник", "Wednesday": "Сряда", "Thursday": "Четвъртък", "Friday": "Петък", "Saturday": "Събота", "Sunday": "Неделя", "January": "Яну<PERSON><PERSON>и", "February": "Февруари", "March": "Ма<PERSON><PERSON>", "April": "<PERSON><PERSON>рил", "May": "<PERSON><PERSON><PERSON>", "June": "Юни", "July": "<PERSON><PERSON><PERSON>", "August": "Август", "September": "Септември", "October": "Октомври", "November": "Ноември", "December": "Декември", "Start Date": "От дата", "End Date": "До дата", "Paid Leave": "Платен отпуск", "Unpaid Leave": "Неплатен отпуск", "Upload File": "Прикачи файл", "Send Request": "Изпрати запитване", "Upload": "Прикачи", "TEAM": "ЕКИП", "DEPARTMENT": "ОТДЕЛ", "Import employees": "Импорт на служители", "Loading Data": "Зареждане на данни", "strWelcomeToWorkTimeHome": "Добре дошли в системата на Microinvest Work Time!", "Bulstat": "ЕИК", "My Companies": "Моите фирми", "Logout": "Изход", "Welcome!": "Добре дошли!", "strWelcomeToWorkTimeFirst": "Успешно се регистрирахте в системата на Microinvest Work Time!", "strAddCompanySideMenu": "Добави фирма", "strCompanyAddedSuccessfully": "Фирмата беше създадена успешно!", "Import all employees": "Импорт на всички служители", "DublicateUserNameError": "Този имейл вече е регистриран в системата!", "BadRequestError": "Неочаквана грешка. Моля, опитайте отново!", "Coworkers": "Сътрудници", "User number": "Потребителски номер", "Email": "E-mail", "SuccesfullSend": "Успешно е подадена заявка за промяна на Вашите данни", "Waiting": "Чака одобрение", "Change password": "Смяна на парола", "strItemsSelected": "избрани елементи", "grOwner": "Собственик", "grDepartmentManager": "Мен<PERSON><PERSON><PERSON><PERSON>р отдел", "grAccountant": "Счетоводител", "SupportChatServiceRole": "Техническа поддръжка чат", "grCompanyManager": "Мен<PERSON><PERSON><PERSON>ър фирма", "grTeamLead": "Ръководител екип", "grHumanResources": "Човешки ресурси (HR)", "grITSupport": "Техническа поддръжка (IT)", "grAccountingFirmSpecialist": "ТРЗ Специалист счетоводна кантора", "grAccountingFirmManager": "Мен<PERSON><PERSON><PERSON><PERSON>р счетоводна кантора", "grCreator": "Създател", "grEmployee": "Служител", "strAccept": "Приеми", "strDecline": "Отхвърли", "strClose": "Затвори", "strGoingForIceCream": "Отивам", "strAlreadyAteIceCream": "Вече ядох", "strCompanyInvitationSent": "Покана за присъединяване към фирма е изпратена успешно!", "Import TRZ Data": "ИМПОРТ НА ДАННИ ОТ ТРЗ ПРО", "EmployeesImportedSuccessfully": "Служителите бяха успешно импортирани!", "EmployeeImportedSuccessfully": "Служителя беше успешно импортиран!", "strChooseRole": "Изберете роля", "strWaitingApproval": "Чака одобрение за", "strCompanyRoleTxt": "Има роля за фирма", "strNext": "Напред", "strSearchByPersonalData": "Име, ЕГН, e-mail, #№", "strSearchByPosition": "Длъжност", "strDepartments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strCategories": "Категория", "strRoleChangedSuccessfully": "Ролята беше успешно променена!", "EIK": "ЕИК", "Pending companies": "Чакащи одобрения фирми", "No companies found": "Няма намерени фирми", "Type of appointment": "Тип назначение", "Invalid email or username": "Невалиден имейл или потребителско име", "E-mail/Username": "Имейл/Потребител", "Fill your email or username": "Въведете вашия имейл или потребителско име", "EGN / LNCH": "ЕГН / ЛНЧ", "Phone number": "Телефон", "Place of birth": "Място на раждане", "Date of birth": "Дата на раждане", "E-mail": "E-mail", "IBAN": "IBAN", "Number": "Номер", "City": "<PERSON>р<PERSON><PERSON>", "District": "Област", "Issued from": "Издадена от", "Municipality": "Община", "Citizenship": "Гражданство", "ID card details": "Данни по лична карта", "Male": "Мъж", "Female": "Жена", "Addresses": "Адреси", "PC": "ПК", "Personal phone": "<PERSON>и<PERSON><PERSON>н тел.", "Work phone": "Служ. тел.", "Personal e-mail": "Личен e-mail", "Work e-mail": "Служ. e-mail", "New address": "Нов адрес", "Other address": "Друг адрес", "Identity card": "<PERSON><PERSON><PERSON><PERSON> карта", "For contact": "За контакт", "For remote work": "За дистанционна работа", "Abroad": "В чужбина", "Address identity card": "Адрес по лична карта", "Address for remote work": "Адрес за дистанционна работа", "Address for contact": "Адрес за контакт", "Address abroad": "Адрес в чужбина", "Address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmployeeRoleSelected": "За всички потребители с роля „Служител, профилът в уеб платформата се създава от Собственика, Управителя, Счетоводителя или HR Мениджъра на фирмата, в която работят. За да получите достъп до електронното си трудово досие и възможност за преглед и заявка на отсъствия, моля, свържете се с оторизираното лице във вашата фирма.", "Address custom": "Адрес служебен", "MOI": "МВР ", "strName": "Име", "strEgn": "ЕГН", "strPosition": "Длъжност", "strDepartment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strTypeOfAppointment": "Назначение", "strNumber": "Номер", "strAbsenceAddedSuccessfully": "Отсъствието беше заявено успешно!", "strSickLeaveAddedSuccessfully": "Болничният беше заявен успешно!", "strStartDateCannotBeAfterEndDate": "Началната дата не може да бъде след крайната дата", "strSelectedDatesOverlapWithExistingLeave": "Избраните дати се застъпват със съществуващо отсъствие", "strMyAbsences": "Моите отсъствия", "strAbsenceWaitingForApproval": "За одобрение", "strAbsenceApproved": "Отсъствието беше одобрено успешно!", "strAbsenceDeclined": "Отсъствието беше отказано успешно!", "strAbsenceDeleted": "Отсъствието беше изтрито успешно!", "strSickLeaveDeleted": "Болничният беше изтрит успешно!", "strSickLeaveApproved": "Болничният беше одобрен успешно!", "strSickLeaveDeclined": "Болничният беше отказан успешно!", "strDeclineAbsenceTitle": "Моля, напишете причина за отхвърляне на отпуска на {0}", "Approve": "Одобри", "Decline": "Отхвърли", "strApproveAbsenceTitle": "Моля, потвърдете, че желаете да одобрите заявката на {0} за {1}, обхващащ периода {2}.", "New employee": "Нов служител", "Add": "Добави", "Block": "Блок", "Apartment": "Апар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "Region": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Issued by": "Издадена от", "ID number": "ЛК номер", "str1PersonalData": "1. Лични данни", "str2IDCardData": "2. Да<PERSON>ни по ЛК", "str3Addresses": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "str4Payroll": "4. Назначение", "strPersonalData": "Лични данни", "strIDCardData": "Данни по ЛК", "strProfileCreationDate": "Данни за създаване на профил", "strBaseData": "Основни данни", "strIDAddress": "Адрес по ЛК", "Email not registered": "Имейлът не е регистриран в системата", "Import companies": "Фирми за импорт", "Change your password": "Сменете Вашата парола", "Absences": "Отпуски", "Sick Leaves": "Болнични", "Passwords does not match": "Паролите не съвпадат", "Passwords": "Пароли", "Old Password": "Стара парола", "New password": "Нова парола", "Repeat password": "Повтори паролата", "Incorrect password": "Старата парола е неправилна", "Password changed successfully": "Паролата беше сменена успешно!", "Password has been changed successfully": "Вашата парола беше успешно сменена!", "Companies list": "Списък фирми", "strDeleteAbsenceTitle": "Моля, потвърдете, че желеате да изтриете отсъствието на {0} за {1}, обхващащ периода {2}.", "Hospital Leave": "Болничен", "Comment": "Коментар", "Send sick leave": "Изпрати болничен", "Maternity Leave": "Майчинство", "strInvalidCode": "Линкът е невалиден или изтекъл! Моля, генерирайте нов линк.", "strSomethingWentWrong": "Нещо се обърка. Моля, опитайте отново с нов линк.", "strEmployee": "Служител", "strEmployeeDeclined": "Служителя беше отказан успешно!", "strEmployeeEGNExists": "Служител с ЕГН {0} има активен договор. Ще бъде добавен нов договор към досието на служителя. Желаете ли да продължите?", "strEmployeeEmailExists": "Служител с E-mail {0} има активен договор. Ще бъде добавен нов договор към досието на служителя. Желаете ли да продължите?", "strAbsenceExistsOnSickLeaveRequest": "Въведеният болничен се припокрива с един или повече отпуски. Периодите на отпуск ще бъдат коригирани след потвърждение на текущото отсъствие от оторизиран потребител", "strAbsenceExistsOnSickLeaveApprove": "Въведеният болничен се припокрива с един или повече отпуски. Периодите на отпуск ще бъдат коригирани. Желаете ли да продъжите?", "No": "Не", "Yes": "Да", "Role": "Роля", "for approval": "за одобрение", "strChangingAbsenceTypeWarning": "Желаете ли да смените типа от отпуск към болничен?", "strChangingHospitalTypeWarning": "Желаете ли да смените типа от болничен към отпуск?", "strAbsenceUpdatedSuccessfully": "Отсъствието беше коригирано успешно!", "strSickLeaveUpdatedSuccessfully": "Болничният беше коригиран успешно!", "Request absence": "Заяви отпуск", "Company successfully edited": "Фирмата беше успешно редактирана", "strReview": "Преглед", "strNoNotifications": "Няма известия", "strPasswordStrenghtIndicator": "Индикаторът ще се запълни изцяло, когато сте спазили изискванията за сигурност. Паролата трябва да съдържа минимум 8 символа с главни и малки латински букви (A-Z, a-z), цифри и специални символи!", "strForgottenPasswordPageDescription": "Моля, въведете новата си парола и я потвърдете, за да възстановите достъпа до профила си.", "strCityVillage": "Град/Село", "Very weak": "Много слаба", "Weak": "Слаба", "Good": "Добра", "Very good": "Много добра", "Excellent": "Отлична", "strContinueAddingEmployee": "Желаете ли да продължите с въвеждането на данни?", "Absence": "Отпуск", "Sick leave": "Болничен", "Paid leave": "Платен отпуск", "From date": "От дата", "To date": "До дата", "Type": "Тип", "Ok": "Ок", "strContractType": "Тип договор", "strAddAbsenceToAllPayrolls": "Желаете ли да подадете заявка за ползване на платен отпуск и по другите си назначения?", "Working days": "Работни дни", "Days worked": "Отработени дни", "strCancelAbsenceDeletion": "Сигурни ли сте, че желаете да отмените изтриването на отсъствието?", "strAbsenceDeletionApproved": "Отсъствието е успешно отменено", "strSickLeaveDeletionApproved": "Болничният е успешно отменен", "strAbsenceDeletionDeclined": "Изтриването на отсъствието е отхвърлено успешно", "strSickLeaveDeletionDeclined": "Изтриването на болничния е отхвърлено успешно", "strApproveAbsenceDeletion": "Сигурни ли сте, че желаете да одобрите изтриването на отсъствието?", "Non-working days": "Почивни дни", "Official holidays": "Офиц. празници", "strSelectedAbsenceAlreadyApproved": "Отсъствието е вече одобрено", "strMaternityLeaveUpdatedSuccessfully": "Майчинството беше коригирано успешно!", "strPaidLeaveUpdatedSuccessfully": "Платеният отпуск беше коригиран успешно!", "strMaternityLeaveDeleted": "Майчинството беше изтрито успешно!", "strPaidLeaveDeleted": "Платеният отпуск беше изтрит успешно!", "strMaternityLeaveApproved": "Майчинството беше одобрено успешно!", "strMaternityLeaveDeclined": "Майчинството беше отказано успешно!", "strPaidLeaveApproved": "Платеният отпуск беше одобрен успешно!", "strPaidLeaveDeclined": "Платеният отпуск беше отказан успешно!", "Invalid employee name": "Невалидно име на служителя", "strChangePasswordMessage": "За да гарантираме сигурността на Вашия профил, е необходимо да смените служебно генерираната парола, с която сте влезли за първи път.", "strAbsenceInfo": "Информация за отсъствие", "Edit absence": "Редакция на отсъствие", "strAbsenceRestored": "Отсъствието беше възстановено успешно!", "strAbsenceHasStartedAndCannotBeEdited": "Отпускът е започнал. Заявка за потвърждение е изпратена на администратора.", "Paid Leave for Training": "Платен отпуск за обучение", "Incidental Leave": "Инцидентен отпуск", "Compensation": "Компенсация", "Select subtype": "Изберете подтип", "Notifications.Hospital.Added.ByEmployee.Push": "{0} подаде заявка за болничен.", "Notifications.Hospital.Approved.Push": "Болничният е приет.", "Notifications.Absences.DeletedRequest.ByEmployee.Push": "{0} подаде заявка за изтриване на одобрено отсъствие.", "Notifications.Absences.Added.ByEmployee.Push": "{0} подаде заявка за отпуск.", "Notifications.Absences.Approved.Push": "Заявката за отпуск е одобрена.", "Notifications.Absences.Edited.ConfirmedAbsence.ByEmployee.Push": "{0} подаде заявка за редакция на одобрено отсъствие.", "Notifications.Absences.Edited.ByEmployee.Push": "{0} подаде заявка за редакция на одобрено отсъствие.", "Notifications.Absences.Declined.Push": "{0} отхвърли заявка за отпуск. Причината за отхвърляне е получена на e-mail.", "Notifications.Absences.Edit.Push": "Отсъствието е редактирано.", "Notifications.Absences.Delete.Push": "Отсъствието е изтрито.", "Notifications.Absences.Delete.ByAdmin.Push": "{0} изтри отсъствие.", "Notifications.Employees.Edited.ByEmployee.Push": "{0} подаде заявка за редакция на личните си данни.", "Notifications.Absences.DeleteDeclined.Push": "{0} отказа да одобри изтриването на отсъствие.", "Notifications.Absences.Edited.ByAdmin.Push": " {0} направи редакция на отсъствие.", "Notifications.Absences.Edited.ConfirmedAbsence.Declined.ByEmployee.Push": "{0} отказа да одобри редакцията на отсъствие.", "Notifications.Employees.Edited.ByAdmin.Push": "{0} редактира личните Ви данни.", "Notifications.Employees.Edited.Addresses.ByAdmin.Push": "{0} редактира адресите Ви.", "Notifications.Employees.Updated.Push": "{името на служител} подаде заявка за редакция на личните си данни.", "Notifications.Employees.EditedAddresses.ByEmployee.Push": "{0} подаде заявка за редакция на адресите си.", "Notifications.Employees.Edit.Confirmed.Push": "Редакцията в досието е направена.", "Notifications.Employees.Edit.Declined.Push": "{0} отказа да одобри направената редакция в досието.", "Address name": "Име на адреса", "strSickLeaveInfo": "Информация за болничен", "Edit sick leave": "Редакция на болничен", "DeclineEdit": "Откажи", "New data:": "Нови данни:", "Before editing:": "Преди редакцията:", "Decline all": "Откажи всички", "Approve all": "Одобри всички", "Training": "Обучение", "State exam/thesis": "Дър<PERSON><PERSON><PERSON><PERSON>н изпит/дипломна работа", "Obtaining a scientific degree": "Получаване на научна степен", "Applying to secondary school": "Кандидатстване в средно училище", "Applying to university": "Кандидатстване в университет", "Official/creative": "Служебен/творчески", "Art. 169, para. 1 of the LC": "чл. 169, ал. 1 от КТ", "Art. 169, para. 3 of the LC": "чл. 169, ал. 3 от КТ", "Art. 169, para. 4 of the LC": "чл. 169, ал. 4 от КТ", "Art. 170, para. 1, item 1 of the LC": "чл. 170, ал. 1, т. 1 от КТ", "Art. 170, para. 1, item 2 of the LC": "чл. 170, ал. 1, т. 2 от КТ", "Art. 161, para. 1 of the LC": "чл. 161, ал. 1 от КТ", "Civil marriage": "Граждански брак", "Blood donation": "Кръводаряване", "Death of a relative": "Смърт на роднина", "Court appearance": "Явяване в съда", "Participation in meetings": "Участие в заседания", "Volunteer in disasters": "Доброволец при бедствия", "Pregnancy/IVF check-up": "Преглед бременност/ин-витро", "Reserve service": "Служба в резерв", "Art. 157, para. 1, item 1 of the LC": "чл. 157, ал. 1, т. 1 от КТ", "Art. 157, para. 1, item 2 of the LC": "чл. 157, ал. 1, т. 2 от КТ", "Art. 157, para. 1, item 3 of the LC": "чл. 157, ал. 1, т. 3 от КТ", "Art. 157, para. 1, item 4 of the LC": "чл. 157, ал. 1, т. 4 от КТ", "Art. 157, para. 1, item 5/5a of the LC": "чл. 157, ал. 1, т. 5/5а от КТ", "Art. 157, para. 1, item 7 of the LC": "чл. 157, ал. 1, т. 7 от КТ", "Art. 157, para. 2 of the LC": "чл. 157, ал. 2 от КТ", "Art. 158, para. 1": "чл. 158, ал. 1", "With insurance service (employer)": "С осигурите<PERSON>ен стаж (осигурител)", "With insurance service (insured)": "С осигурит<PERSON><PERSON><PERSON>н стаж (осигурен)", "Without insurance service (employer)": "Без осигурителен стаж (осигурител)", "Without insurance service (insured)": "Без осигурите<PERSON>ен стаж (осигурен)", "Childcare up to 8 years": "За отглеждане на дете до 8 години", "Preparation and taking an exam": "Подготовка и явяване на изпит", "State exam/thesis SU": "Дър<PERSON><PERSON><PERSON><PERSON>н изпит/дипломна работа СУ", "State exam/thesis University": "Държ<PERSON><PERSON><PERSON>н изпит/дипломна работа ВУЗ", "Dissertation": "Дисертация", "Official/creative leave": "Служебен/творчески отпуск", "Absence without leave": "Самоотлъчване", "Art. 167a of the LC": "чл. 167а от КТ", "Art. 170, para. 2 of the LC": "чл. 170, ал. 2 от КТ", "Art. 171, para. 1, item 1 of the LC": "чл. 171, ал. 1, т. 1 от КТ", "Art. 171, para. 1, item 2 of the LC": "чл. 171, ал. 1, т. 2 от КТ", "Art. 171, para. 1, item 3 of the LC": "чл. 171, ал. 1, т. 3 от КТ", "Art. 171, para. 1, item 4 of the LC": "чл. 171, ал. 1, т. 4 от КТ", "Country": "Държава", "TermsAndConditions": "Общи правила за ползване и политика за поверителност на WORKTIME.BG", "LastUpdate": "Последна актуализация: 01.08.2025", "GeneralTermsOfUse": "I. ОБЩИ ПРАВИЛА ЗА ПОЛЗВАНЕ", "GeneralProvisions": "1. Общи положения", "TermsDescription1": "1.1. Настоящите Общи правила уреждат условията за ползване на уеб платформата, предоставяна от Микроинвест ЕООД, ЕИК 831826092, със седалище и адрес на управление: гр. София, ул. Бойчо Бойчев №12, представлявано от Виктор Павлов (наричано по-долу \"Доставчик\").", "TermsDescription2": "1.2. С достъпа до платформата и създаването на потребителски профил, Вие се съгласявате с настоящите Правила и се задължавате да ги спазвате.", "PlatformNature": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р на платформата", "PlatformDescription1": "2.1. Платформата представлява портал за управление на човешки ресурси, чрез който служители, мениджъри, собственици и външни лица (счетоводители, IT специалисти и др.) осъществяват взаимоотношения и обмяна на данни, свързани с трудовите отношения.", "PlatformDescription2": "2.2. Функционалностите на платформата включват:", "PlatformFeatures": "• подаване и одобрение на заявления за отпуски; • възлагане и изпълнение на задачи; • подаване на документи (болнични, дипломи, сертификати, автобиографии); • заявяване на справки и взаимодействие с HR отдели; • поддържане на профил с лични и трудови данни.", "AccessAndRegistration": "3. Достъп и регистрация", "AccessDescription1": "3.1. Достъпът до платформата се осъществява чрез регистрация с имейл и парола. Потвърждението на настоящите Правила е задължително условие при регистрация и при всяка последваща смяна на паролата.", "AccessDescription2": "3.2. Профилът е личен, свързан с конкретно физическо лице, и се използва само от него.", "AccessDescription3": "3.3. Достъп до определени функционалности зависи от ролята на потребителя (напр. служи<PERSON>е<PERSON>, мен<PERSON><PERSON><PERSON><PERSON><PERSON>, администратор, представител на външна фирма).", "UserRightsAndObligations": "4. Права и задължения на потребителя", "UserObligations": "4.1. Потребителят се задължава:", "UserObligationsList": "• да използва платформата добросъвестно и само по предназначение; • да не предоставя достъп до своя профил на трети лица; • да съхранява паролата си по сигурен начин.", "UserRights": "4.2. Потребителят има право:", "UserRightsList": "• на достъп до и корекция на своите лични данни; • на изтриване на профила си при прекратяване на договорните отношения с фирмата; • да получи информация за това какви данни се съхраняват за него.", "SupportAndAdminAccess": "5. Поддръжка и достъп до профил от администратор", "SupportDescription1": "5.1. В определени случаи, като част от техническата поддръжка, служители на Микроинвест ЕООД или оторизирани лица могат да имат достъп до профила Ви, след предварително заявено искане и само с цел разрешаване на технически проблем или поддръжка.", "SupportDescription2": "5.2. Всички действия на поддръжката се записват и могат да бъдат проверявани по всяко време от фирмата клиент.", "SubscriptionAndPayment": "6. А<PERSON><PERSON><PERSON>мент и заплащане", "SubscriptionDescription1": "6.1. Платформата се предоставя срещу абонамент, платен от юридическото лице (фирмата), с което сте свързани като служител или партньор.", "SubscriptionDescription2": "6.2. Няма индивидуално заплащане от страна на физическите лица, ползващи системата.", "LiabilityLimitation": "7. Ограничение на отговорност", "LiabilityDescription": "7.1. Микроинвест ЕООД не носи отговорност за:", "LiabilityList": "• действия на потребителите в нарушение на настоящите правила; • съдържанието на прикачените документи от потребителите; • евентуални загуби, причинени от неправилна употреба на платформата.", "TermsChanges": "8. Промени в условията", "TermsChangesDescription1": "8.1. Микроинвест ЕООД си запазва правото да променя настоящите правила. При всяка промяна потребителят ще бъде уведомен при следващото влизане или смяна на паролата.", "TermsChangesDescription2": "8.2. Продължаването на използването на платформата след уведомление за промени се счита за съгласие с новите правила.", "PrivacyPolicy": "II. ПОЛИТИКА ЗА ПОВЕРИТЕЛНОСТ", "Introduction": "1. Въведение", "PrivacyIntroduction": "Настоящата Политика за поверителност описва как Микроинвест ЕООД, ЕИК: 831826092, със седалище в гр. София, ул. Бойчо Бойчев №12 (наричано по-долу „Микроинвест\", „ние\", „нас\"), събира, използва, съхранява и защитава Вашите лични данни при използване на уеб платформата за управление на човешки ресурси.", "DataCategories": "2. Категории обработвани лични данни", "DataCategoriesList": "• Име и фамилия, им<PERSON><PERSON><PERSON>, ЕГ<PERSON>, пол, телефон, адрес • Длъжност, професионален опит, образование, сертификати • Документи като автобиографии, болнични листове, заявления и др.", "ProcessingBasis": "3. Основания за обработка", "ProcessingBasisList": "• Изпълнение на договор/трудови задължения • Законова необходимост • Изрично съгласие (при чувствителни данни) • Легитимен интерес на администратора", "ProcessingPurposes": "4. Цели на обработката", "ProcessingPurposesList": "• Регистрация и достъп до платформата • Управление на отпуск, болнични, задачи и документи • Вътрешнофирмена комуникация и архивиране • Сигурност и поддръжка на платформата", "DataStorage": "5. Съхранение и местоположение на данните", "DataStorageDescription": "Данните се съхраняват на сървъри в България, в дейта център на Виваком България ЕАД. Срокът на съхранение зависи от договора между работодателя и Микроинвест.", "DataAccess": "6. Достъп до данни и споделяне", "DataAccessList": "• Само с оправомощени лица от страна на работодателя • По заявка – с поддръжката на Микроинвест • Без споделяне с трети страни с рекламна цел • Към държавни органи – само при законово задължение", "YourRights": "7. Вашите права", "YourRightsList": "• Право на достъп, корекция и изтриване • Право на преносимост • Оттегляне на съгласие • Възражение срещу обработка • Жалба до КЗЛД", "DataSecurity": "8. Сигурност на данните", "DataSecurityList": "• HTTPS криптиране • Контрол на достъпа и роли • Логване на действията • Въвеждане на двуфакторна автентикация", "CookiesPolicy": "9. Политика за бисквитки(Cookies)", "CookiesDescription": "Платформата използва бисквитки (cookies), които представляват малки файлове с данни, съхранявани на Вашето устройство с цел улесняване и сигурност на достъпа до услугите.", "CookiesTypes": "9.1. Видове бисквитки, които използваме", "CookiesTypesList": "• Сесийни бисквитки – необходими за правилното функциониране на платформата по време на активна сесия; • Функционални бисквитки – за удостоверяване, сигурност и запазване на предпочитания;", "NoMarketingCookies": "Не се използват маркетингови, рекламни или проследяващи бисквитки.", "CookiesManagement": "9.2 Управление на бисквитките", "CookiesManagementDescription": "Можете да контролирате или изтривате бисквитки чрез настройките на Вашия браузър. Ограничаването или изтриването на бисквитките може да повлияе на правилното функциониране на платформата.", "Contact": "10. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContactInfo": "Микроинвест ЕООД Адрес: гр. С<PERSON><PERSON><PERSON><PERSON>, ул. Бойчо Бойчев №12 Имейл: <EMAIL> Телефон: +359 2 955 55 15", "AcceptanceStatement": "С използването на платформата Вие потвърждавате, че сте запознати и приемате Общите правила и Политиката за поверителност.", "Hospital Categories": "Категории болнични", "Caring for Sick Family Member": "Гледане на болен член от семейството", "Work Accident": "Трудова злополука", "Occupational Disease": "Професионална болест", "Unpaid Leave for Temporary Disability": "Неплатен отпуск за временна неработоспособност", "Sick Leave During Pregnancy": "Болничен по бременност", "Sick Leave After Birth": "Болничен след раждане", "Unpaid Leave for Pregnancy and Birth": "Неплатен отпуск за бременност и раждане", "Maternity Leave 135-410 Days": "Отпуск за майка след раждане от 135 до 410 ден", "Paternity Leave for Child Care Over 6 Months": "Отпуск за баща за гледане на дете над 6 месеца", "Leave Up to 15 Days for Child Birth": "Отпуск до 15 дни при раждане на дете", "Leave for Child Care Up to 2 Years": "Отпуск за отглеждане на дете до 2 години", "Leave for Child Adoption Up to 5 Years": "Отпуск при осиновяване на дете до 5-годишна възраст", "Unpaid Leave for Child Adoption Up to 5 Years": "Неплатен отпуск при осиновяване на дете до 5-годишна възраст", "Paternity Leave for Child Adoption Up to 5 Years": "Отпуск за баща при осиновяване на дете до 5-годишна възраст", "Leave for Child Care Up to 8 Years by Father (Adopter)": "Отпуск за отглеждане на дете до 8-годишна възраст от бащата (осиновителя)", "Sign out": "Напускане на компанията", "Delete company": "Изтриване на компанията", "strCompanyDeleted": "Компанията беше успешно изтрита.", "Contract type": "Тип договор", "Worktime": "Работно време", "Workplace": "Месторабота", "Reason KT": "Основание по КТ", "Additional terms": "Допълнителни условия", "Entry date": "Постъпване", "Last change": "Последна промяна", "Experience in company": "Стаж във фирмата", "Work experience": "Трудов стаж", "Expirience": "Стаж", "Dates": "Дати", "Contract Dates and Periods": "Дати и периоди по договор", "Current payroll": "Текущо назначение", "Contract date": "Дата на сключване", "Date from": "В сила от", "Validity period": "Срок на валидност", "Contract term date": "Срок на договора", "Date to": "В сила до", "Codes": "Кодове", "Entry": "Постъпване", "KID code": "Код по КИД", "NKPD code": "Код по НКПД", "EKATTE code": "Код по ЕКАТТЕ", "First day of insurance": "Първи ден в осигуряване", "Last day of insurance": "Последен ден в осигуряване", "First working day": "Първи работен ден", "Probationary period": "Изпитателен срок", "Notice period": "Период на предизвестие", "Last working day": "Последен работен ден", "Termination date": "Дата на прекратяване", "Experience": "Стаж", "Professional experience": "Професио<PERSON><PERSON>ен стаж", "Company experience": "Стаж във фирмата", "Daily Worktime": "Работно време", "Annex": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AP": "ДС", "EmpC": "ТД", "MCA": "ДУК", "ConS": "ГД", "SEP": "СОЛ", "Main data": "Основни данни", "Landlord": "Наемодател", "External": "Вън<PERSON>ен", "Dividend": "Диви<PERSON><PERSON><PERSON>т", "ZDDFLIncome": "Вид доход по ЗДДФЛ", "NKPD position": "Длъжност по НКПД", "strCompanyLeft": "Заявката за напускане на фирма е успешна.", "Continue": "Продължи", "Your edit is pending approval.": "Вашата редакция чака одобрение.", "The edit is already reviewed.": "Търсената заявка вече е прегледана от друг администратор.", "Effective from": "В сила от", "Contract data": "Данни за договора", "Joined on": "Постъпване", "y": "г.", "m": "м.", "d": "д.", "hours": "часа", "strEmployeeExistsExportPayrollFromTRZ": "Служител {0} вече съществува в Work Time. За да добавите ново назначение към неговото досие, е необходимо да експортирате данните от Microinvest ТРЗ и ЛС Pro.", "Confirmation E-mail sent to": "Успешно е изпратен e-mail за потвърждение на ", "E-mail confirmed successfully": "Вашата регистрация беше успешна!", "strProfileUpdatedSuccessfully": "Профила ви беше успешно обновен.", "Your email confirmation is awaiting.": "Вашият e-mail изчаква потвърждение.", "strEmailConfirmationServerError": "Няма връзка със сървъра. Опитайте по-късно.", "strEmailConfirmationLinkExpired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, който последвахте е изтекъл. Ако желаете, повторете отначало процеса по регистрация.", "Enter additional terms...": "Добави допълнителни условия...", "Additional terms saved successfully.": "Допълнителните условия са записани успешно"}