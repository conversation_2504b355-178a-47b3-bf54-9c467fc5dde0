﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Roles;

namespace Gateway.Handlers.Companies
{
    public class ChangeUserRoleHandler(IWorkTimeApiConnection workTimeApiConnection) : IRequestHandler<ChangeEmployeeRoleRequest, IResult>
    {
        public async Task<IResult> Handle(ChangeEmployeeRoleRequest request, CancellationToken cancellationToken)
        {
            var response = await workTimeApiConnection.ChangeEmployeeRoleAsync(request);
            if (!response.IsSuccessStatusCode)
                return Results.BadRequest("Невалиден отговор от SSO-то");

            if (!request.IsNewlyAddedEmployee)
                return Results.Ok();

            var employeeResponse = await workTimeApiConnection.AddEmployeeCompanyAsync(new WorkTimeApi.Common.Requests.Companies.AddEmployeeCompanyRequest
            {
                CompanyId = request.CompanyId,
                UserId = request.UserId,
                Status = EmployeeStatus.Active
            });

            return employeeResponse.IsSuccessStatusCode
                ? Results.Ok()
                : Results.BadRequest("Невалиден отговор от WorkTimeApi-то");
        }
    }
}
