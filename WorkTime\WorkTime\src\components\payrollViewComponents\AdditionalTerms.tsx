import React, { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import Container from "../Container";
import Fieldset from "../Fiedlset/Fieldset";
import Legend from "../Fiedlset/Legend";
import Translator, { translate } from "../../services/language/Translator";
import { authenticatedPut } from "../../services/worktimeConnectionService";
import { toast } from "react-toastify";

const AdditionalTermsContainer = styled(Container)`
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 35rem);
`;

const AdditionalTermsFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  margin: 1rem;
  padding: 1rem;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const FieldsetRow = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0.2rem;
  width: 100%;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 100%;
`;

const EditableTextarea = styled.textarea`
  width: 100%;
  min-height: 8rem;
  border: none;
  border-radius: 0;
  padding: 0;
  font-family: Segoe UI;
  font-weight: Regular;
  text-decoration: none;
  font-size: 1rem;
  color: var(--input-field-color);
  background: transparent;
  resize: none;
  outline: none;
  overflow: hidden;

  &:focus {
    outline: none;
  }

  &:disabled {
    background: transparent;
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

const SaveButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
  margin-right: 1rem;
`;

const SaveButton = styled.button`
  padding: 0.8rem 2rem;
  border-radius: 2rem;
  border: 0.2rem solid var(--additional-details-button-border);
  background: var(--additional-details-button-background);
  color: var(--additional-details-button-font);
  font-family: Segoe UI;
  font-weight: Regular;
  text-decoration: none;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    border-color: var(--additional-details-button-border-hover);
    color: var(--additional-details-button-font-hover);
  }

  &:disabled {
    border-color: var(--additional-details-button-border-disabled);
    cursor: auto;
  }
`;

interface AdditionalTermsProps {
  data: any;
  onDataRefresh?: () => void;
  isEditable?: boolean;
  payrollId?: string;
}

const AdditionalTerms = ({
  data,
  onDataRefresh,
  isEditable = false,
  payrollId,
}: AdditionalTermsProps) => {
  const [localValue, setLocalValue] = useState(data?.additionalTerms || "");
  const [isLoading, setIsLoading] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    setLocalValue(data?.additionalTerms || "");
  }, [data?.additionalTerms]);

  const autoResize = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${
        textareaRef.current.scrollHeight / 16
      }rem`;
    }
  };

  useEffect(() => {
    if (isEditable && textareaRef.current) {
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
          const length = textareaRef.current.value.length;
          textareaRef.current.setSelectionRange(length, length);
          autoResize();
          setIsFocused(true);
        }
      }, 100);
    }
  }, [isEditable]);

  useEffect(() => {
    autoResize();
  }, [localValue]);

  const handleSave = async () => {
    if (!payrollId || isLoading) {
      return;
    }

    setIsLoading(true);

    try {
      await authenticatedPut<void>(`payrolls/${payrollId}/additional-terms`, {
        payrollId: payrollId,
        additionalTerms: localValue,
      });

      if (onDataRefresh) {
        onDataRefresh();
      }

      toast.success(translate("Additional terms saved successfully."));
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setLocalValue(e.target.value);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setIsFocused(false);
    }, 150);
  };

  return (
    <AdditionalTermsContainer>
      <AdditionalTermsFieldset>
        <Legend>
          <Translator getString="Additional terms" />
        </Legend>
        <FieldsetRow>
          <ValueColumn>
            {isEditable ? (
              <EditableTextarea
                ref={textareaRef}
                value={localValue}
                onChange={handleTextChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
                placeholder={translate("Enter additional terms...")}
              />
            ) : (
              <EditableTextarea
                ref={textareaRef}
                value={localValue}
                onChange={handleTextChange}
                disabled={true}
              />
            )}
          </ValueColumn>
        </FieldsetRow>
      </AdditionalTermsFieldset>
      {isEditable && (
        <>
          <SaveButtonContainer>
            <SaveButton onClick={handleSave} disabled={isLoading || !isFocused}>
              <Translator getString="Save" />
            </SaveButton>
          </SaveButtonContainer>
        </>
      )}
    </AdditionalTermsContainer>
  );
};

export default AdditionalTerms;
