import { AnnexPayrollSummaryDTO } from "../models/DTOs/payrolls/AnnexPayrollSummaryDTO";
import { LengthOfServiceDTO } from "../models/DTOs/payrolls/LengthOfService";
import { PayrollSummaryDTO } from "../models/DTOs/payrolls/PayrollSummaryDTO";

export const calculateExperienceFromDates = (
  fromDate: Date | null,
  toDate: Date | null
): LengthOfServiceDTO | null => {
  if (!fromDate) return null;

  // const startDate = new Date(fromDate);
  // const endDate = toDate ? new Date(toDate) : new Date();

  const startDate = new Date(2024, 9, 1);
  const endDate = new Date(2025, 9, 9);

  if (startDate > endDate) return null;

  let years = Math.max(endDate.getFullYear() - startDate.getFullYear() - 1, 0);
  let months = 0;
  let days = 0;

  if (startDate.getFullYear() === endDate.getFullYear()) {
    months = endDate.getMonth() + 1 - (startDate.getMonth() + 1);
  } else {
    months = 12 - (startDate.getMonth() + 1) + endDate.getMonth();
  }

  if (startDate.getDate() === 1) {
    months++;
  } else {
    days +=
      new Date(startDate.getFullYear(), startDate.getMonth(), 0).getDate() -
      startDate.getDate() -
      1;
  }

  if (
    endDate.getDate() ===
    new Date(endDate.getFullYear(), endDate.getMonth(), 0).getDate()
  ) {
    months++;
  } else {
    days += endDate.getDate();
  }

  if (days < 0) {
    months--;
    const lastDayOfPrevMonth = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      0
    ).getDate();
    days += lastDayOfPrevMonth;
  }

  if (months < 0) {
    years--;
    months += 12;
  }

  if (days >= 30) {
    const additionalMonths = Math.floor(days / 30);
    months += additionalMonths;
    days = days % 30;
  }

  if (months >= 12) {
    const additionalYears = Math.floor(months / 12);
    years += additionalYears;
    months = months % 12;
  }

  return {
    id: "",
    employeeId: "",
    years: years,
    months: months,
    days: days,
  };
};

export const getProfessionalExperienceInCompany = (
  data: PayrollSummaryDTO | AnnexPayrollSummaryDTO,
  effectiveToDate?: Date | null
): LengthOfServiceDTO | null => {
  if (
    "professionalЕxperienceInCompany" in data &&
    data.professionalЕxperienceInCompany
  ) {
    return data.professionalЕxperienceInCompany;
  }

  const toDate = effectiveToDate !== undefined ? effectiveToDate : data.toDate;
  return calculateExperienceFromDates(data.fromDate, toDate);
};
