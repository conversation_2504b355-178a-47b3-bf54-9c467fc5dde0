﻿using Gateway.Common.Globals;
using Gateway.Common.ResponseObjects;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration.Companies;
using MediatR;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Companies;
using UR = Microinvest.TransferFiles.Tools.ResponseObjects;

namespace Gateway.Handlers.Companies
{
	public class JoinCompanyHandler : IRequestHandler<JoinCompanyRequest, IResult>
	{
		private readonly IUserRegistrationConnection _userRegistrationConnection;
		private readonly GlobalUser _globalUser;
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public JoinCompanyHandler(IWorkTimeApiConnection workTimeApiConnection, IUserRegistrationConnection userRegistrationConnection, GlobalUser globalUser)
		{
			_workTimeApiConnection = workTimeApiConnection;
			_userRegistrationConnection = userRegistrationConnection;
			_globalUser = globalUser;
		}

		public async Task<IResult> Handle(JoinCompanyRequest request, CancellationToken cancellationToken)
		{
			if (request.Bulstat is null)
				return Results.BadRequest();

			var userRegistrationCompanies = await _userRegistrationConnection.ApproveEmployeeForCompany(_globalUser.Email, request.Bulstat);
			if (!userRegistrationCompanies.IsSuccessStatusCode)
				return Results.StatusCode((int)userRegistrationCompanies.StatusCode);

			var senderaCompany = await userRegistrationCompanies.Content.ReadFromJsonAsync<UR.CompanyResponseObject>(cancellationToken);

			if (senderaCompany is null)
				return Results.NoContent();

			var companyRequest = new CreateCompanyRequest()
			{
				Name = senderaCompany.Name,
				Bulstat = senderaCompany.Bulstat,
				ContactName = senderaCompany.ContactName,
                UserRegistrationCompanyId = senderaCompany.Id
			};

			var createCompanyResponse = await _workTimeApiConnection.CreateCompanyAsync(companyRequest);

			if (createCompanyResponse is null)
				return Results.NoContent();

			var companyDTO = await createCompanyResponse.Content.ReadFromJsonAsync<WorktimeCompanyResponse>();

			if (companyDTO is null)
				return Results.NoContent();

			var employeeCompanyRequest = new AddEmployeeCompanyRequest()
			{
				CompanyId = companyDTO.Id,
				UserId = _globalUser.Id,
				Status = EmployeeStatus.Active
			};

			var addEmployeeCompanyResponse = await _workTimeApiConnection.AddEmployeeCompanyAsync(employeeCompanyRequest);
			if (!addEmployeeCompanyResponse.IsSuccessStatusCode)
				return Results.BadRequest();

			var companyReponseObject = new SenderaCompanyResponseObject
			{
				Id = senderaCompany.Id,
				Name = companyDTO.Name,
				Bulstat = senderaCompany.Bulstat,
				ContactName = companyDTO.ContactName,
				UserStatus = senderaCompany.UserStatus,
			};

			return Results.Ok(companyReponseObject);
		}
	}
}