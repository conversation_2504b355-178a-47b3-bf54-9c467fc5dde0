import { styled } from "styled-components";
import GridView, { GridViewDiv } from "../../../components/GridView";
import { ICompaniesGridViewEntity } from "../../../models/Interfaces/ICompaniesGridViewEntity";
import Label from "../../../components/Inputs/Label";
import Container from "../../../components/Container";
import Image from "../../../components/Image";
import companyImg from "../../../assets/images/companies/samplecompanylogo.svg";

const CompanyLogo = styled(Image)`
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 1rem;
  width: 5rem;
  height: 5rem;
  cursor: pointer;
`;

const NameContainer = styled(Container)`
  font: Segoe UI;
  color: var(--first-label-color-gridView);
  opacity: 1;
  text-align: center;
  position: absolute;
  width: 90%;
  left: 5%;
  bottom: 13%;
  height: 4rem;
  cursor: pointer;
  overflow: hidden;
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const FitText = styled.div`
  font-size: 1rem;
  max-height: 100%;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
`;

const EikLabel = styled(Label)`
  font: Segoe UI;
  color: var(--second-label-color-gridView);
  opacity: 1;
  font-size: 1rem;
  text-align: center;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const EikContainer = styled(Container)`
  position: absolute;
  bottom: 1rem;
  text-align: center;
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
`;

interface CompaniesGridElementsProps {
  data: ICompaniesGridViewEntity[];
  imgSize: number;
  handleClick: (id: string) => void;
  highlightCompanyId?: string;
}

const CompaniesGridElements = ({
  data,
  handleClick,
  highlightCompanyId,
}: CompaniesGridElementsProps) => {
  return (
    <GridView
      data={data}
      renderElement={(company) => {
        const isHighlighted = highlightCompanyId === company.id;
        return (
          <GridViewDiv
            $isHighlighted={isHighlighted}
            key={company.id}
            data-testid={`grid-view-div-${company.id}`}
            onClick={() => {
              handleClick(company.id);
            }}
          >
            <CompanyLogo
              src={companyImg}
              data-testid={`company-logo-${company.id}`}
            />
            <NameContainer data-testid={`name-container-${company.id}`}>
              <FitText data-testid={`fit-text-${company.id}`}>
                {company.name}
              </FitText>
            </NameContainer>
            <EikContainer data-testid={`eik-container-${company.id}`}>
              <EikLabel data-testid={`eik-label-bulstat-${company.id}`}>
                Bulstat
              </EikLabel>
              :
              <EikLabel data-testid={`eik-label-${company.id}`}>
                {company.bulstat}
              </EikLabel>
            </EikContainer>
          </GridViewDiv>
        );
      }}
    />
  );
};

export default CompaniesGridElements;
