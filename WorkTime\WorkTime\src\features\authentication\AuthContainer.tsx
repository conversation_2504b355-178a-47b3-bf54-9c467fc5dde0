import styled from "styled-components";
import Container from "../../components/Container";
import VerticalLine from "../../components/VerticalLine";
import Login from "./Login";
import GoogleLogin from "./Providers/GoogleLogin";
import FacebookLogin from "./Providers/FacebookLogin";
import MicrosoftLogin from "./Providers/MicrosoftLogin";
import { useSearchParams } from "react-router-dom";
import Label from "../../components/Inputs/Label";
import Button from "../../components/Inputs/Button";
import { useState } from "react";
import Registration from "./Registration";
import { AuthMode } from "../../models/Enums/AuthMode";
import MainWindowContainer from "../../components/MainWindowContainer";
import BackgroundImage from "../../assets/images/background-image.svg";

const MainContainer = styled(MainWindowContainer)`
  display: flex;
  flex-wrap: wrap;
  flex: 1 1 auto;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 10rem);
  position: relative;

  &::before {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 546.727px;
    height: 383.518px;
    background-image: url(${BackgroundImage});
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom right;
    pointer-events: none;
    z-index: 0;
  }
`;

const DataContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2.5rem;
  box-sizing: border-box;
  width: 30rem;
  position: relative;
  z-index: 10;

  @media (max-width: 980px) {
    flex: 100%;
    padding: 0 2.5rem 0 2.5rem;
  }
`;

const HeaderContainer = styled(Container)`
  width: 100%;
  height: 3.25rem;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const CurrentMenuLabel = styled(Label)`
  position: absolute;
  left: 1rem;
  font-size: 1.3rem;
`;

const NextMenuButton = styled(Button)`
  position: absolute;
  right: 0.25rem;
  background: none !important;
  color: var(--button-no-background-color);
  font-size: rem;

  &:hover {
    color: var(--button-no-background-color-hover);
  }
`;

const SeparatorContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 30rem;
  position: relative;
  z-index: 1;

  @media (max-width: 600px) {
    display: none;
  }
`;

const Separator = styled(VerticalLine)`
  height: 25rem;
  margin-bottom: 2.5rem;

  @media (max-width: 980px) {
    display: none;
  }
`;

const AuthProvidersContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 25rem;
  padding: 0 2.5rem;
  height: 300px;
  position: relative;
  z-index: 1;

  @media (max-width: 980px) {
    padding: 0 2.5rem 2.5rem 2.5rem;
  }
`;

const AuthContainerContent = styled(Container)`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  height: 430px;
`;

interface Props {
  initialAuthMode: AuthMode;
}

const AuthContainer = ({ initialAuthMode }: Props) => {
  const [searchParams] = useSearchParams();
  const [authMode, setAuthMode] = useState(initialAuthMode);
  const returnAfterLogin = searchParams.get("returnAfterLogin");

  return (
    <MainContainer>
      <DataContainer>
        <HeaderContainer data-testid="header-container">
          <CurrentMenuLabel data-testid="current-menu-label">
            {authMode === AuthMode.Login ? "Login" : "Registration"}
          </CurrentMenuLabel>
          {authMode === AuthMode.Login ? (
            <NextMenuButton
              data-testid="next-menu-button"
              label="Registration"
              onClick={() => setAuthMode(AuthMode.Registration)}
            ></NextMenuButton>
          ) : (
            <NextMenuButton
              data-testid="next-menu-button"
              label="Login"
              onClick={() => setAuthMode(AuthMode.Login)}
            ></NextMenuButton>
          )}
        </HeaderContainer>
        <AuthContainerContent>
          {authMode === AuthMode.Login ? (
            <Login
              data-testid="login-component"
              returnAfterLogin={returnAfterLogin ?? undefined}
            />
          ) : (
            <Registration data-testid="registration-component" />
          )}
        </AuthContainerContent>
      </DataContainer>
      {import.meta.env.VITE_GATEWAY_API !== "Testing" &&
        import.meta.env.VITE_GATEWAY_API !== "Production" && (
          <>
            <SeparatorContainer data-testid="separator-container">
              <Separator data-testid="separator" />
            </SeparatorContainer>
            <AuthProvidersContainer data-testid="auth-providers-container">
              <GoogleLogin
                data-testid="google-login"
                returnAfterLogin={returnAfterLogin ?? undefined}
              />
              <FacebookLogin
                data-testid="facebook-login"
                returnAfterLogin={returnAfterLogin ?? undefined}
              />
              <MicrosoftLogin
                data-testid="microsoft-login"
                returnAfterLogin={returnAfterLogin ?? undefined}
              />
            </AuthProvidersContainer>
          </>
        )}
    </MainContainer>
  );
};

export default AuthContainer;
