using Gateway.Common.Results;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.DTOs.Notifications;
using WorkTimeApi.Common.DTOs.Users;

namespace WorkTimeApi.Services.Interfaces.Users
{
    public interface IUserService
    {
        Task ConfirmUserInvitationAsync(CompanyInvitationRequest companyInvitationRequest);

        Task<Result<UserDTO>> UpdateUserHasSignedInAsync(Guid id, bool value);

        Task<UserDTO> GetUserAsync(Guid userId);

        Task<UserDTO> AddUserAsync(UserDTO userDTO);

        Task<List<CompanyNotificationSettingsDTO>> GetNoticationSettingsAsync(Guid userId);

        Task<UserDTO> UpdateUserAsync(UserDTO userDTO);

        Task<IResult> UpdateUserEmailAsync(UserDTO userDTO);

        Task<IResult> ConfirmEmailCodeAsync(string email,string code);
    }
}
