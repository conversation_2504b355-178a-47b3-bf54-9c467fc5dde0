import React from "react";
import styled from "styled-components";
import { ChangeEvent, useEffect, useState } from "react";
import Searchbar from "../../../components/Inputs/Searchbar";
import CompaniesGridView from "./CompaniesGridView";
import CompaniesListView from "./CompaniesListView";
import gridView from "../../../assets/images/companiesView/gridviewIcon.svg";
import listView from "../../../assets/images/companiesView/listviewIcon.svg";
import listIconHover from "../../../assets/images/companiesView/listviewIconHover.svg";
import gridIconHover from "../../../assets/images/companiesView/gridviewIconHover.svg";
import plusicon from "../../../assets/images/companies/plus.svg";
import { useAppDispatch, useAppSelector } from "../../../app/hooks";
import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import { companiesState, onGetCompanies, Status } from "../companiesActions";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { companyFilterService } from "../../../services/companies/CompanyFilterService";
import { useMenu } from "../../MenuContext";
import Loading from "../../../components/Loading";
import { Header } from "../../../components/Header";
import { HeaderType } from "../../../models/Enums/HeaderType";
import { LOCAL_STORAGE_IS_COMPANIES_VIEW_LIST_VIEW } from "../../../constants/local-storage-constants";
import NoCompaniesIndex from "../NoCompaniesIndex";
import { PendingCompaniesList } from "./PendingCompaniesList";
import { translate } from "../../../services/language/Translator";
import Label from "../../../components/Inputs/Label";

const MainContainer = styled(MainWindowContainer)`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem;
  height: 45rem;
  overflow-y: auto;
`;

const Container = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: clamp(30%, 100rem, 90%);
  margin: 0rem;
  gap: 1rem;
`;

const MainContentArea = styled.div`
  flex: 0 1 auto;
  display: flex;
  justify-content: flex-start;
  min-width: 0;
`;

const SideButtonArea = styled.div`
  flex: 0 0 auto;
  display: flex;  
  align-items: center;
  margin-top: 6rem;
`;

const SearchbarContainer = styled.div`
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  justify-content: center;
  width: 64rem;
  margin: 1rem;
`;

const UnifiedSearchContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 1rem 0rem;
  width: 67rem;
  position: relative;
`;

const ImageGridListView = styled.div<{ $isListView: boolean }>`
  position: absolute;
  right: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
   background-repeat: no-repeat;
  background-position: center;

  ${({ $isListView }) =>
    $isListView
      ? `
        background-image: url(${gridView});
        width: 1.6rem;
        height: 1.6rem;
        background-size: 1.6rem 1.6rem;
        transition: background-image 0.2s ease-in-out, left 0.2s ease-in-out;

        &:hover {
          background-image: url(${gridIconHover});
          /* по желание – различен hover размер */
          background-size: 1.6rem 1.6rem;
        }
      `
      : `
        /* показваме list */
        background-image: url(${listView});
        width: 1.5rem;
        height: 1.5rem;
        background-size: 1.5rem 1.5rem;
        transition: background-image 0.2s ease-in-out, left 0.2s ease-in-out;

        &:hover {
          background-image: url(${listIconHover});
          /* по желание – различен hover размер */
          background-size: 1.5rem 1.5rem;
        }
      `}
`;

const CircleContainer = styled.button`
  --circle-size: 4rem;
  height: var(--circle-size);
  width: var(--circle-size);
  background-color: var(--circle-container-backround-gridView);
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &::before {
    content: url(${plusicon}); 
    display: block;
    line-height: 0; 
    transform: scale(0.75);        
    transform-origin: center;
      }

  &:hover {
    background-color: var(--circle-container-hover-backround-gridView);
  }
`;

const PositionedCircleContainer = styled(CircleContainer)`
  position: absolute;
  right: -3rem;
  top: 50%;
  transform: translateY(-50%);
`;

const StyledHeader = styled(Header)`
  margin-top: 3.5rem;
  width: 20rem;
  text-align: center;
  font-weight: 350;
  font-size: 22px;
`;

const CompanyName = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1.2rem;
  text-align: left;
  cursor: pointer;
  vertical-align: middle;
  overflow: hidden;
`;

const Companies = () => {
  const [textInput, setTextInput] = useState("");
  const [isListView, setIsListView] = useState<boolean>(
    localStorage.getItem(LOCAL_STORAGE_IS_COMPANIES_VIEW_LIST_VIEW) === "true"
  );
  const { activeCompanies, pendingCompanies, status } =
    useAppSelector(companiesState);
  const { companyFilter, mapToFilteredDTOs } = companyFilterService();
  const [filteredCompanies, setFilteredCompanies] = useState<CompanyDTO[]>(
    companyFilter(textInput)
  );
  const { changeView, toggleMenu } = useMenu();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(onGetCompanies());
  }, [dispatch]);

  useEffect(() => {
    const filteredCompaniesArray = companyFilter(textInput);
    setFilteredCompanies(filteredCompaniesArray);
  }, [activeCompanies, pendingCompanies]);

  const handleSetViewModeClick = () => {
    localStorage.setItem(
      LOCAL_STORAGE_IS_COMPANIES_VIEW_LIST_VIEW,
      `${!isListView}`
    );

    setIsListView(!isListView);
  };

  const companiesFilter = (searchText: string) => {
    const filteredCompaniesArray = companyFilter(searchText);
    const mappedFilteredData = mapToFilteredDTOs(filteredCompaniesArray);
    setFilteredCompanies(mappedFilteredData);
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const searchText = e.target.value;
    setTextInput(searchText);
    companiesFilter(searchText);
  };

  const handleAddCompany = () => {
    toggleMenu();
    changeView("myCompanies", "companyLabel", { initialTab: "create" });
  };

  if (status === Status.Fetching) return <Loading />;

  if (
    status === Status.Fetched &&
    activeCompanies.length === 0 &&
    pendingCompanies.length === 0
  )
    return <NoCompaniesIndex />;

  return (
    <MainContainer data-testid="main-container">
      <StyledHeader
        content="strWelcomeToWorkTimeHome"
        headerType={HeaderType.H2}
        data-testid="header"
      />
      <UnifiedSearchContainer data-testid="unified-search-container">
        <SearchbarContainer data-testid="searchbar-container">
          <Searchbar
            handleChange={handleChange}
            value={textInput}
            type="text"
            label="strGetInput"
            placeholder={""}
            data-testid="searchbar"
          />
          <ImageGridListView
            $isListView={isListView}
            onClick={handleSetViewModeClick}
            data-testid="image-grid-list-view"
          />
        </SearchbarContainer>
        {isListView && (
          <PositionedCircleContainer
            onClick={handleAddCompany}
            data-testid="circle-container"
          />
        )}
      </UnifiedSearchContainer>

      <Container data-testid="companies-container">
        <MainContentArea>
          {isListView ? (
            <CompaniesListView
              data={filteredCompanies}
              data-testid="companies-list-view"
            />
          ) : (
            <CompaniesGridView
              data={filteredCompanies}
              data-testid="companies-grid-view"
            />
          )}
        </MainContentArea>
        {!isListView && (
          <SideButtonArea>
            <CircleContainer
              onClick={handleAddCompany}
              data-testid="circle-container"
            />
          </SideButtonArea>
        )}
      </Container>
      {filteredCompanies.length === 0 && pendingCompanies.length === 0 && (
        <CompanyName>{translate("No companies found")}</CompanyName>
      )}

      <PendingCompaniesList
        pendingCompanies={pendingCompanies}
        data-testid="pending-companies-list"
      />
    </MainContainer>
  );
};
export default Companies;
