﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Services.Interfaces.Payrolls;

namespace WorkTimeApi.Handlers.Payrolls
{
    public class UpdateAdditionalTermsHandler : IRequestHandler<UpdateAdditionalTermsRequest, IResult>
    {
        private readonly IPayrollsService _payrollService;

        public UpdateAdditionalTermsHandler(IPayrollsService payrollService)
        {
            _payrollService = payrollService;
        }

        public async Task<IResult> Handle(UpdateAdditionalTermsRequest request, CancellationToken cancellationToken)
        {
            await _payrollService.UpdateAdditionalTermsAsync(request);
            return Results.Ok();
        }
    }
}
