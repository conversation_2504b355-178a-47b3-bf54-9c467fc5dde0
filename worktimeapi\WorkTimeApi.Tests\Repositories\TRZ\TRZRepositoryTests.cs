using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Models.TRZ;
using WorkTimeApi.Database.Repositories.TRZ;

namespace WorkTimeApi.Tests.Repositories.TRZ
{
    public class TRZRepositoryTests
    {
        private sealed class TestDbContextFactory(DbContextOptions<WorkTimeApiDbContext> options, IConfiguration configuration) : IDbContextFactory<WorkTimeApiDbContext>
        {
            public WorkTimeApiDbContext CreateDbContext() => new(options, configuration);

            public ValueTask<WorkTimeApiDbContext> CreateDbContextAsync(CancellationToken cancellationToken = default)
                => new(CreateDbContext());
        }

        private static IDbContextFactory<WorkTimeApiDbContext> CreateDbFactory(string dbName)
        {
            var configData = new Dictionary<string, string?>
            {
                { "WorkTimeDefaultCompanyId", "99999999-9999-9999-9999-************" }
            };
            var configuration = new ConfigurationBuilder().AddInMemoryCollection(configData).Build();

            var options = new DbContextOptionsBuilder<WorkTimeApiDbContext>()
                .UseInMemoryDatabase(databaseName: dbName)
                .EnableSensitiveDataLogging()
                .Options;

            return new TestDbContextFactory(options, configuration);
        }

        private static async Task SeedRequiredAsync(WorkTimeApiDbContext context, Guid companyId, Guid structureLevelId)
        {
            var company = new Company { Id = companyId, Name = "TestCo", Bulstat = "*********" };
            var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
            var structureLevel = new StructureLevel { Id = structureLevelId, Name = "Dept", Type = StructureLevelType.Department, CompanyId = companyId };

            context.Companies.Add(company);
            context.Users.Add(user);
            context.StructureLevels.Add(structureLevel);
            await context.SaveChangesAsync();
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_MergesEventsByIdAndAggregatesFields()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            var companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            var structureLevelId = Guid.Parse("*************-2222-2222-************");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);

                var existingUserId = Guid.NewGuid();
                var existingUser = new User { Id = existingUserId, Email = "<EMAIL>" };
                seedCtx.Users.Add(existingUser);
                var existingEmployeeId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa");
                seedCtx.Employees.Add(new Employee
                {
                    Id = existingEmployeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = existingUserId,
                    User = existingUser,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var employeeId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa");

            var employee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = new List<TRZPayroll>()
            };

            var payrollId = Guid.NewGuid();
            var eventId = Guid.NewGuid();

            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = []
            };

            var e1 = new TRZEvent
            {
                Id = eventId,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                EventType = TRZEventType.ПлатенПоДругиЧленове,
                StartDate = new DateTime(2025, 1, 10),
                EndDate = new DateTime(2025, 1, 12),
                Duration = 2,
                IsHospital = false
            };
            var e2 = new TRZEvent
            {
                Id = eventId,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                EventType = TRZEventType.ПлатенПоДругиЧленове,
                StartDate = new DateTime(2025, 1, 5),
                EndDate = new DateTime(2025, 1, 15),
                Duration = 5,
                IsHospital = false
            };

            payroll.Events.Add(e1);
            payroll.Events.Add(e2);
            employee.PendingPayrolls.Add(payroll);

            var employees = new List<Employee> { employee };

            var result = await repository.AddTRZEmployeesAsync(employees, companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees
                .Include(e => e.PendingPayrolls)
                .ThenInclude(pp => pp.Events)
                .First(e => e.Id == employeeId);

            var dbPayroll = dbEmployee.PendingPayrolls.First(p => p.Id == payrollId);
            Assert.Single(dbPayroll.Events);

            var merged = dbPayroll.Events.First();
            Assert.Equal(eventId, merged.Id);
            Assert.Equal(new DateTime(2025, 1, 5), merged.StartDate);
            Assert.Equal(new DateTime(2025, 1, 15), merged.EndDate);
            Assert.Equal(7, merged.Duration);
            Assert.Equal(TRZEventType.ПлатенГодишенОтпуск, merged.EventType);
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_MergesOnlyDuplicates_KeepsDistinctEvents()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var duplicateId = Guid.NewGuid();
            var uniqueId = Guid.NewGuid();

            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = []
            };

            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 2, 2), EndDate = new DateTime(2025, 2, 3), Duration = 2 });
            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 2, 1), EndDate = new DateTime(2025, 2, 5), Duration = 4 });
            payroll.Events.Add(new TRZEvent { Id = uniqueId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 3, 1), EndDate = new DateTime(2025, 3, 1), Duration = 1 });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync([pendingEmployee], companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var events = dbEmployee.PendingPayrolls.First().Events.ToList();
            Assert.Equal(2, events.Count);
            Assert.Contains(events, e => e.Id == duplicateId && e.Duration == 6 && e.StartDate == new DateTime(2025, 2, 1) && e.EndDate == new DateTime(2025, 2, 5));
            Assert.Contains(events, e => e.Id == uniqueId && e.Duration == 1);
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_SingleEvent_RemainsUnchanged()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var eid = Guid.NewGuid();
            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = new List<TRZEvent>()
            };

            payroll.Events.Add(new TRZEvent { Id = eid, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 4, 10), EndDate = new DateTime(2025, 4, 12), Duration = 3, EventType = TRZEventType.ПлатенПоДругиЧленове });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync(new List<Employee> { pendingEmployee }, companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var evs = dbEmployee.PendingPayrolls.First().Events.ToList();
            Assert.Single(evs);
            var ev = evs.First();
            Assert.Equal(eid, ev.Id);
            Assert.Equal(3, ev.Duration);
            Assert.Equal(TRZEventType.ПлатенПоДругиЧленове, ev.EventType);
            Assert.Equal(new DateTime(2025, 4, 10), ev.StartDate);
            Assert.Equal(new DateTime(2025, 4, 12), ev.EndDate);
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_MergeNullDurations_TreatsNullAsZero()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("dddddddd-dddd-dddd-dddd-dddddddddddd");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var duplicateId = Guid.NewGuid();
            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = new List<TRZEvent>()
            };

            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 5, 2), EndDate = new DateTime(2025, 5, 3), Duration = null });
            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 5, 1), EndDate = new DateTime(2025, 5, 5), Duration = 4 });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync([pendingEmployee], companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var merged = dbEmployee.PendingPayrolls.First().Events.Single();
            Assert.Equal(4, merged.Duration);
            Assert.Equal(new DateTime(2025, 5, 1), merged.StartDate);
            Assert.Equal(new DateTime(2025, 5, 5), merged.EndDate);
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_EventsWithEmptyIds_ShouldNotBeGrouped()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = new List<TRZEvent>()
            };

            // Add events with empty Ids - these should NOT be grouped
            payroll.Events.Add(new TRZEvent
            {
                Id = Guid.Empty,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 6, 1),
                EndDate = new DateTime(2025, 6, 2),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });
            payroll.Events.Add(new TRZEvent
            {
                Id = Guid.Empty,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 6, 3),
                EndDate = new DateTime(2025, 6, 4),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync([pendingEmployee], companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var events = dbEmployee.PendingPayrolls.First().Events.ToList();

            // Should have 2 separate events (not grouped)
            Assert.Equal(2, events.Count);

            // Both events should maintain their original properties (EF will assign new GUIDs)
            Assert.Contains(events, e => e.StartDate == new DateTime(2025, 6, 1) && e.EndDate == new DateTime(2025, 6, 2) && e.Duration == 2);
            Assert.Contains(events, e => e.StartDate == new DateTime(2025, 6, 3) && e.EndDate == new DateTime(2025, 6, 4) && e.Duration == 2);

            // EventType should remain unchanged (not set to ПлатенГодишенОтпуск)
            Assert.All(events, e => Assert.Equal(TRZEventType.ПлатенПоДругиЧленове, e.EventType));
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_MixedValidAndEmptyIds_OnlyValidIdsAreGrouped()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("ffffffff-ffff-ffff-ffff-ffffffffffff");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var validId = Guid.NewGuid();
            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = new List<TRZEvent>()
            };

            // Add events with valid Ids (should be grouped)
            payroll.Events.Add(new TRZEvent
            {
                Id = validId,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 7, 1),
                EndDate = new DateTime(2025, 7, 2),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });
            payroll.Events.Add(new TRZEvent
            {
                Id = validId,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 7, 3),
                EndDate = new DateTime(2025, 7, 4),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });

            // Add events with empty Ids (should NOT be grouped)
            payroll.Events.Add(new TRZEvent
            {
                Id = Guid.Empty,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 7, 5),
                EndDate = new DateTime(2025, 7, 6),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });
            payroll.Events.Add(new TRZEvent
            {
                Id = Guid.Empty,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 7, 7),
                EndDate = new DateTime(2025, 7, 8),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync([pendingEmployee], companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var events = dbEmployee.PendingPayrolls.First().Events.ToList();

            // Should have 3 events total: 1 merged valid event + 2 separate empty events
            Assert.Equal(3, events.Count);

            // One event should be the merged valid event
            var mergedValidEvent = events.FirstOrDefault(e => e.Id == validId);
            Assert.NotNull(mergedValidEvent);
            Assert.Equal(new DateTime(2025, 7, 1), mergedValidEvent.StartDate);
            Assert.Equal(new DateTime(2025, 7, 4), mergedValidEvent.EndDate);
            Assert.Equal(4, mergedValidEvent.Duration);
            Assert.Equal(TRZEventType.ПлатенГодишенОтпуск, mergedValidEvent.EventType);

            // Two events should be the separate events (originally with empty Ids, now with EF-generated GUIDs)
            var separateEvents = events.Where(e => e.Id != validId).ToList();
            Assert.Equal(2, separateEvents.Count);
            Assert.All(separateEvents, e => Assert.Equal(TRZEventType.ПлатенПоДругиЧленове, e.EventType));
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_OnlyValidIdsWithDuplicates_ShouldBeGrouped()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("*************-7777-7777-************");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var validId1 = Guid.NewGuid();
            var validId2 = Guid.NewGuid();
            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = new List<TRZEvent>()
            };

            // Add duplicate events with valid Ids (should be grouped)
            payroll.Events.Add(new TRZEvent
            {
                Id = validId1,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 8, 1),
                EndDate = new DateTime(2025, 8, 2),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });
            payroll.Events.Add(new TRZEvent
            {
                Id = validId1,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 8, 3),
                EndDate = new DateTime(2025, 8, 4),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });

            // Add single event with different valid Id (should remain separate)
            payroll.Events.Add(new TRZEvent
            {
                Id = validId2,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                StartDate = new DateTime(2025, 8, 5),
                EndDate = new DateTime(2025, 8, 6),
                Duration = 2,
                EventType = TRZEventType.ПлатенПоДругиЧленове
            });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync([pendingEmployee], companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var events = dbEmployee.PendingPayrolls.First().Events.ToList();

            // Should have 2 events: 1 merged + 1 separate
            Assert.Equal(2, events.Count);

            // One event should be the merged validId1 event
            var mergedEvent = events.FirstOrDefault(e => e.Id == validId1);
            Assert.NotNull(mergedEvent);
            Assert.Equal(new DateTime(2025, 8, 1), mergedEvent.StartDate);
            Assert.Equal(new DateTime(2025, 8, 4), mergedEvent.EndDate);
            Assert.Equal(4, mergedEvent.Duration);
            Assert.Equal(TRZEventType.ПлатенГодишенОтпуск, mergedEvent.EventType);

            // One event should be the separate validId2 event
            var separateEvent = events.FirstOrDefault(e => e.Id == validId2);
            Assert.NotNull(separateEvent);
            Assert.Equal(new DateTime(2025, 8, 5), separateEvent.StartDate);
            Assert.Equal(new DateTime(2025, 8, 6), separateEvent.EndDate);
            Assert.Equal(2, separateEvent.Duration);
            Assert.Equal(TRZEventType.ПлатенПоДругиЧленове, separateEvent.EventType);
        }
    }
}
