import React, { useState } from "react";
import styled from "styled-components";
import Container from "../../../../components/Container";
import Label from "../../../../components/Inputs/Label";
import Fieldset from "../../../../components/Fiedlset/Fieldset";
import Legend from "../../../../components/Fiedlset/Legend";
import { translate } from "../../../../services/language/Translator";
import AdditionalTerms from "../../../../components/payrollViewComponents/AdditionalTerms";
import FieldRow from "../../../../components/payrollViewComponents/FieldRow";
import { PayrollSummaryDTO } from "../../../../models/DTOs/payrolls/PayrollSummaryDTO";
import { formatDate } from "../../../../utils/payrollUtils";

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 30%;
  gap: 2rem;
  align-items: flex-start;
`;

const ResponsiveGridContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  height: 100%;
  width: 100%;

  @media (min-width: 1600px) {
    grid-template-columns: repeat(2, minmax(400px, 1fr));
  }
`;

const CustomLayoutContainer = styled(Container)`
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  grid-template-rows: auto auto;
  gap: 1rem;
  height: 100%;
  width: 100%;

  & > :nth-child(1) {
    grid-column: 1;
    grid-row: 1 / span 2;
  }

  & > :nth-child(2) {
    grid-column: 2;
    grid-row: 1;
  }

  & > :nth-child(3) {
    grid-column: 2;
    grid-row: 2;
  }
`;

const RightColumn = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 17%;
  height: 100%;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  border-radius: 2.2rem;
  justify-content: flex-start;
  align-items: flex-start;
`;

const MenuText = styled(Label)<{ $isSelected?: boolean }>`
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
  text-align: left;
  color: ${(props) =>
    props.$isSelected ? "var(--profile-menu-text-hover-color)" : "#bdc4d6"};
  cursor: pointer;
  width: 100%;
  user-select: none;
`;

const MenuItem = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 0.5rem;
  padding-top: 0;
  align-items: flex-start;

  &:hover {
    ${MenuText} {
      color: var(--profile-department-name-font-color);
    }
  }
`;

const MenuLine = styled.div`
  width: 100%;
  height: 1px;
  background-color: #bdc4d6;
  opacity: 0.5;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  padding: 1rem;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

interface EmployeePayrollViewProps {
  payrollData: PayrollSummaryDTO;
  payrollId: string;
  isAdmin: boolean;
  onDataRefresh?: () => void;
}

const EmployeePayrollView = ({
  payrollData,
  payrollId,
  isAdmin,
  onDataRefresh,
}: EmployeePayrollViewProps) => {
  const [selectedTab, setSelectedTab] = useState("main-contract");

  const tabs = [
    {
      id: "main-contract",
      label: `${formatDate(payrollData?.fromDate)} ${translate("Landlord")} ${
        payrollData?.contractNumber
      }`,
      type: "main",
      data: payrollData,
    },
    {
      id: "additional-terms",
      label: "Additional terms",
      type: "additional-terms",
    },
  ];

  const currentTab = tabs.find((tab) => tab.id === selectedTab);
  const dataToUse = currentTab?.data || payrollData;

  const renderMain = () => (
    <CustomLayoutContainer>
      <StyledFieldset>
        <Legend>{`${translate("Landlord")} ${
          payrollData?.contractNumber || ""
        } - ${translate("Main data")}`}</Legend>
        <FieldRow label="strDepartment" value={dataToUse?.structureLevelName} />
        <FieldRow label="Workplace" value={dataToUse?.workplace} />
        <FieldRow
          label="ZDDFLIncome"
          value={dataToUse?.incomeType?.identifier || ""}
          includeEmptyRow
        />
        <FieldRow
          label="Additional terms"
          value={dataToUse?.additionalTerms}
          showThreeDotsIcon={true}
          onThreeDotsClick={() => setSelectedTab("additional-terms")}
        />
      </StyledFieldset>

      <StyledFieldset>
        <Legend>Dates</Legend>
        <FieldRow
          label="Contract date"
          value={formatDate(dataToUse?.contractDate)}
        />
        <FieldRow
          label="Termination date"
          value={formatDate(dataToUse?.toDate)}
        />
      </StyledFieldset>
    </CustomLayoutContainer>
  );

  const renderAdditionalDetails = () => (
    <AdditionalTerms
      data={dataToUse}
      isEditable={isAdmin}
      payrollId={payrollId}
      onDataRefresh={onDataRefresh}
    />
  );

  const renderContent = () => {
    switch (currentTab?.type) {
      case "main":
        return renderMain();
      case "additional-terms":
        return renderAdditionalDetails();
      default:
        return renderMain();
    }
  };

  return (
    <WrapperContainer>
      {renderContent()}
      <RightColumn>
        {tabs.map((tab) => (
          <MenuItem key={tab.id} onClick={() => setSelectedTab(tab.id)}>
            <MenuText $isSelected={selectedTab === tab.id}>
              {tab.label}
            </MenuText>
            <MenuLine />
          </MenuItem>
        ))}
      </RightColumn>
    </WrapperContainer>
  );
};

export default EmployeePayrollView;
