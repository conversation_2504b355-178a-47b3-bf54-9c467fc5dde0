import React from "react";
import styled, { css } from "styled-components";
import { useCompany } from "../CompanyContext";
import { useNavigate, useLocation } from "react-router-dom";
import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import Label from "../../../components/Inputs/Label";
import Container from "../../../components/Container";
import companyImg from "../../../assets/images/companies/samplecompanylogo.svg";
import { setCompanyLocalStorage } from "../../../services/companies/companiesService";
import { translate } from "../../../services/language/Translator";
import { useUserEmployee } from "../../UserEmployeeContext";
import { DefaultPermissions } from "../../../constants/permissions";
import { UserEmployeeDTO } from "../../../models/DTOs/users/UserEmployeeDTO";
import { initUserEmployee } from "../../../services/users/userService";
import { loadEmployeePayrollsAction } from "../../payroll/employeePayrollActions";
import { useAppDispatch } from "../../../app/hooks";
import { getEmployeePayrolls } from "../../../services/employees/employeePayrollsService";

const Image = styled.img`
  height: 2.2rem;
  width: auto;  
  cursor: pointer;    
`;

const CompanyName = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1rem;
  text-align: left;
  cursor: pointer;
  vertical-align: middle;
  overflow: hidden;
  width: 18rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex-shrink: 0;
`;

const EIK = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1rem;
  text-align: left;
  position: relative;
  cursor: pointer;
  text-align: center;
  white-space: nowrap;
`;

const EIKContainer = styled(Container)`
  width: 12rem;
  white-space: nowrap;
`;

const ContactNameCotnainer = styled(Container)`
  text-align: end;
  flex-shrink: 0;
  width: 25rem;
  overflow: hidden;
  margin-left: auto;
`;

const ContactName = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1rem;
  text-align: right;
  cursor: pointer;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`;

const CompanyContainer = styled(Container)<{ $isHighlighted?: boolean }>`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 1rem;
  padding-right: 1.3rem;
  border: none;
  height: 3.5rem;
  border-radius: 1.8rem;
  border: 0.2rem solid transparent;
  margin: 0.1rem;
  cursor: pointer;
  gap: 1.4rem;
  transition: background-color 0.3s ease-in-out;
  &:hover {
    background-color: var(--listview-backround-hover-button);
    border-radius: 2rem;
  }
  ${({ $isHighlighted }) =>
    $isHighlighted &&
    css`
      background-color: var(--listview-backround-hover-button);
    `}
`;

const CompaniesContainer = styled(Container)`
  display: grid;
  align-items: flex-start;
  width: clamp(35%, 60rem, 70%);
  justify-self: start;
`;

interface ListViewProps {
  data: CompanyDTO[] | null;
  highlightCompanyId?: string;
}

const CompaniesListView: React.FC<ListViewProps> = ({
  data,
  highlightCompanyId,
}) => {
  const dispatch = useAppDispatch();
  const { setCompany } = useCompany();
  const { setUserEmployee } = useUserEmployee();
  const navigate = useNavigate();
  const location = useLocation();

  const selectCompany = async (company: CompanyDTO) => {
    setCompany(company);
    setCompanyLocalStorage(company);

    dispatch({ type: "CHANGE_COMPANY", company: company });

    await initUserEmployee().then(async (response: UserEmployeeDTO) => {
      setUserEmployee(response);

      const employeePayrollsResponse = await getEmployeePayrolls(company.id);
      dispatch(loadEmployeePayrollsAction(employeePayrollsResponse));

      if (
        response.permissions.includes(DefaultPermissions.Employees.Write) ||
        employeePayrollsResponse.length > 1
      )
        navigate(`/${company.id}/employees`);
      else
        navigate(
          `/${company.id}/employees/0/${response.employeeId}/${
            employeePayrollsResponse[0]?.payrollId ?? ""
          }`
        );
    });
  };

  const highlightId = highlightCompanyId || location.state?.highlightCompanyId;

  return (
    <CompaniesContainer>
      {data &&
        data.length > 0 &&
        data.map((c) => (
          <CompanyContainer
            key={c.name}
            onClick={() => selectCompany(c)}
            $isHighlighted={highlightId === c.id}
          >
            <Image src={companyImg} alt={c.name} />
            <CompanyName>{c.name}</CompanyName>
            <EIKContainer>
              <EIK>{translate("EIK:")}</EIK> <EIK>{c.bulstat}</EIK>
            </EIKContainer>
            <ContactNameCotnainer>
              <ContactName>{translate("MOL:") + " "}</ContactName>
              <ContactName>{c.contactName}</ContactName>
            </ContactNameCotnainer>
          </CompanyContainer>
        ))}
    </CompaniesContainer>
  );
};

export default CompaniesListView;
