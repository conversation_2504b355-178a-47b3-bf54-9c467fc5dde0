﻿using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using Gateway.Models;
using Gateway.Models.Responses;
using MediatR;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.Employees
{
    public class GetCoworkersHandler(IWorkTimeApiConnection workTimeApiConnection, GlobalUser globalUser) : IRequestHandler<GetCoworkersRequest, IResult>
    {
        public async Task<IResult> Handle(GetCoworkersRequest request, CancellationToken cancellationToken)
        {
            var employeesResponse = await workTimeApiConnection.LoadCoworkersAsync(new LoadCoworkersRequest { UserId = globalUser.Id });

            var employees = await employeesResponse.Content.ReadFromJsonAsync<List<EmployeeDTO>>(cancellationToken: cancellationToken) ?? [];

            var result = new GetCoworkersResponse();

            foreach (var employee in employees)
            {
                if (employee.Status == EmployeeStatus.CompanyToApprove)
                {
                    result.PendingEmployees.Add(employee);
                    continue;
                }

                result.Coworkers.Add(new CoworkerDTO
                {
                    CompanyId = employee.CompanyId,
                    Employee = employee ?? new EmployeeDTO(),
                    Roles = [.. employee?.EmployeeRoles.Select(er => er.Role) ?? []]
                });
            }

            return Results.Ok(result);
        }
    }
}
