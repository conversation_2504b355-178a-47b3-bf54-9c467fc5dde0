import React, { useEffect, useMemo } from "react";
import { AbsencesPageEmployeesList } from "../AbsencesPageEmploeesList";
import airplane from "../../../assets/images/attendancies/airplane-big.svg";
import heart from "../../../assets/images/attendancies/hearth-big.svg";
import styled from "styled-components";
import {
  ButtonContainer,
  CardContainer,
  Icon,
  MainCardContainer,
  StyledButton,
  StyledLabel,
  TopContainer,
} from "../styles";
import { Employee } from "../useFilteredEmployees";
import { useAppSelector, useAppDispatch } from "../../../app/hooks";
import { AbsenceHospitalDTO } from "../../../models/DTOs/absence/AbsenceHospitalDTO";
import { selectPayrolls } from "../../payroll/payrollsActions";
import { calculateDaysInSelectedYear } from "../../../services/calendar/calendarService";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import { EventType } from "../../../models/DTOs/absence/EventType";
import { LightPayrollDTO } from "../../../models/DTOs/payrolls/LightPayrollDTO";
import {
  onCurrentYearHolidaysLoaded,
  selectCurrentYearHolidays,
} from "../../holidays/holidayActions";

const CustomIcon = styled(Icon)`
  margin-top: 0.4em;
`;

interface MyAbsencesContainerProps {
  selectedPayroll?: LightPayrollDTO;
  selectedYear?: number;
  selectedMonth?: number;
  handleAddAbsence: () => void;
  selectedEmployee?: Employee;
  onSelectEmployee: (employee: Employee | undefined) => void;
}

export const MyAbsencesContainer: React.FC<MyAbsencesContainerProps> = ({
  selectedPayroll,
  selectedYear,
  selectedMonth,
  handleAddAbsence,
  selectedEmployee,
  onSelectEmployee,
}) => {
  const payrollsState = useAppSelector(selectPayrolls);
  const currentYear = new Date().getFullYear();
  const dispatch = useAppDispatch();
  const holidaysForCurrentYear = useAppSelector(selectCurrentYearHolidays);

  useEffect(() => {
    dispatch(onCurrentYearHolidaysLoaded());
  }, [dispatch]);

  const leaves = useMemo(() => {
    const result = payrollsState.payrolls
      .filter((payroll) => payroll.id === selectedPayroll?.workTimeId)
      .map((payroll) => payroll.leaves)
      .flat();

    return result;
  }, [selectedPayroll, payrollsState.payrolls]);

  const usedSickLeaveDays = useMemo(() => {
    const result = leaves
      .filter(
        (leave) =>
          leave.isHospital === true &&
          (leave.status === AbsenceStatus.Approved ||
            leave.status === AbsenceStatus.EditedByAdmin)
      )
      .reduce(
        (total: number, leave) =>
          total +
          calculateDaysInSelectedYear(
            leave,
            currentYear.toString() ?? "",
            holidaysForCurrentYear
          ),
        0
      );

    return result;
  }, [selectedPayroll, leaves, holidaysForCurrentYear]);

  const usedLeaveDays = useMemo(() => {
    const result = leaves
      .filter(
        (leave: AbsenceHospitalDTO) =>
          leave.isHospital === false &&
          (leave.typeIdentifier === EventType.ПлатенГодишенОтпуск ||
            leave.typeIdentifier ===
              EventType.ПлатенГодишенОтпускЗаМиналаГодина) &&
          (leave.status === AbsenceStatus.Approved ||
            leave.status === AbsenceStatus.EditedByAdmin)
      )
      .reduce((total: number, leave: AbsenceHospitalDTO) => {
        return (
          total +
          calculateDaysInSelectedYear(
            leave,
            currentYear.toString() ?? "",
            holidaysForCurrentYear
          )
        );
      }, 0);
    return result;
  }, [selectedPayroll, leaves, holidaysForCurrentYear]);

  const remainingPaidLeaveDaysForCurrentYear = useMemo(() => {
    return (
      (selectedPayroll?.annualPaidLeave ?? 0) +
      (selectedPayroll?.additionalAnnualPaidLeave ?? 0) +
      (selectedPayroll?.annualPaidLeavePastYears ?? 0) -
      usedLeaveDays
    );
  }, [usedLeaveDays, selectedPayroll]);

  return (
    <>
      <TopContainer data-testid="top-container">
        <MainCardContainer data-testid="main-card-container-1">
          <CustomIcon
            src={airplane}            
            alt="airplane"
            data-testid="icon-airplane"
            
          />
          <CardContainer
            variant="AbsencesContainer"
            data-testid="card-container-1"
          >
            <StyledLabel
              children={
                remainingPaidLeaveDaysForCurrentYear < 0
                  ? "0"
                  : remainingPaidLeaveDaysForCurrentYear.toString()
              }
              boldness={500}
              fontSize={30}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-remaining-days"
            />
            <StyledLabel
              children={
                remainingPaidLeaveDaysForCurrentYear == 1 ? "day" : "days"
              }
              boldness={300}
              fontSize={14}
              color="var(--attendancies-right-view-card-container-smallfont)"
              data-testid="label-days"    
              style={{ marginBottom: "0.7em" }}         
            />
            <StyledLabel
              children="Remaining"
              boldness={400}
              fontSize={15}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-remaining"
            />
            <StyledLabel
              children="Paid annual"
              boldness={400}
              fontSize={15}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-paid-leave"
            />
            <StyledLabel
              children="leave"
              boldness={400}
              fontSize={15}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-paid-leave"              
            />
          </CardContainer>
        </MainCardContainer>
        <MainCardContainer data-testid="main-card-container-2">
          <CustomIcon
            src={heart}            
            alt="heart"
            data-testid="icon-heart"
          />
          <CardContainer
            variant="AbsencesContainer"
            data-testid="card-container-2"
          >
            <StyledLabel
              children={
                usedSickLeaveDays < 0 ? "0" : usedSickLeaveDays.toString()
              }
              boldness={500}
              fontSize={30}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-remaining-days-2"
            />
            <StyledLabel
              children={usedSickLeaveDays == 1 ? "day" : "days"}
              boldness={300}
              fontSize={14}
              color="var(--attendancies-right-view-card-container-smallfont)"
              data-testid="label-days-2"
              style={{ marginBottom: "0.7em" }}  
            />
            <StyledLabel
              children="Used"
              boldness={400}
              fontSize={15}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-used"
            />
            <StyledLabel
              children="Sick Leave"
              boldness={400}
              fontSize={15}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-sick-leave"
            />
          <div style={{ display: "flex", alignItems: "center", gap: "0.3em" }}> 
            <StyledLabel
              children={"in"}          
              boldness={400}
              fontSize={15}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-in"
            />
            <StyledLabel
              children={`${currentYear}`}
              boldness={400}
              fontSize={16}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-year"
            />
            </div> 
          </CardContainer>
        </MainCardContainer>
      </TopContainer>
      <ButtonContainer data-testid="button-container">
        <StyledButton
          label="Confirm"
          onClick={handleAddAbsence}
          data-testid="button-confirm"
        />
      </ButtonContainer>
      <AbsencesPageEmployeesList
        selectedPayroll={selectedPayroll}
        selectedEmployee={selectedEmployee}
        onSelectEmployee={onSelectEmployee}
        data-testid="absences-page-employees-list"
      />
    </>
  );
};
