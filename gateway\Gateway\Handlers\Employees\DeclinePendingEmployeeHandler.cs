using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.Employees
{
    public class DeclinePendingEmployeeHandler(IWorkTimeApiConnection workTimeApiConnection) : IRequestHandler<DeclinePendingEmployeeRequest, IResult>
    {
        public async Task<IResult> Handle(DeclinePendingEmployeeRequest request, CancellationToken cancellationToken)
        {
            var response = await workTimeApiConnection.DeclinePendingEmployeeAsync(request);
            return response.IsSuccessStatusCode
                ? Results.Ok()
                : Results.Problem(await response.Content.ReadAsStringAsync(cancellationToken));
        }
    }
}