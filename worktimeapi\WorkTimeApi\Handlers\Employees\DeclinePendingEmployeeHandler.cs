using MediatR;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Employees
{
    public class DeclinePendingEmployeeHandler(IEmployeesService employeesService) : IRequestHandler<DeclinePendingEmployeeRequest, IResult>
    {
        public async Task<IResult> Handle(DeclinePendingEmployeeRequest request, CancellationToken cancellationToken)
        {
            await employeesService.UpdateEmployeeStatusAsync(request.EmployeeId, EmployeeStatus.Declined);
            return Results.Ok();
        }
    }
}

