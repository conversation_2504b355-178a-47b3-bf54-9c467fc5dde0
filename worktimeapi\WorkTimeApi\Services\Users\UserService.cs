using AutoMapper;
using Gateway.Common.Results;
using Microsoft.EntityFrameworkCore;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.DTOs.Notifications;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.EnumUtilities.Validation;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Database.Repositories.Interfaces.Users;
using WorkTimeApi.Services.Interfaces.Notifications;
using WorkTimeApi.Services.Interfaces.Users;

namespace WorkTimeApi.Services.Users
{
    public class UserService : IUserService
    {
        private readonly IEmployeesRepository _employeesRepository;
        private readonly IUserRepository _usersRepository;
        private readonly IMapper _mapper;
        private readonly IEmailsNotificationService _emailsNotificationService;
        private readonly IConfiguration _configuration;

        public UserService(IEmployeesRepository employeesRepository, I<PERSON>apper mapper, IUserRepository usersRepository, IEmailsNotificationService emailsNotificationService, IConfiguration configuration)
        {
            _employeesRepository = employeesRepository;
            _mapper = mapper;
            _usersRepository = usersRepository;
            _emailsNotificationService = emailsNotificationService;
            _configuration = configuration;
        }

        public async Task ConfirmUserInvitationAsync(CompanyInvitationRequest companyInvitationRequest)
        {
            await _employeesRepository.UpdateUserEmployeeStatusAsync(companyInvitationRequest.UserId,
                companyInvitationRequest.CompanyId,
                companyInvitationRequest.IsCompanyAccepted ? EmployeeStatus.Active : EmployeeStatus.Declined);
        }

        public async Task<Result<UserDTO>> UpdateUserHasSignedInAsync(Guid id, bool value)
        {
            var user = await _usersRepository.GetUserAsync(id);
            if (user is null)
                return new ValidationResult(new List<int>((int)EmployeeValidationErrorsEnum.EmployeeDoesNotExist));

            user.HasSignedIn = value;

            if (user.HasSignedIn)
                user.CodePassword = null;

            await _usersRepository.UpdateUserHasSignedInAsync(user);

            return _mapper.Map<UserDTO>(user);
        }

        public async Task<UserDTO> GetUserAsync(Guid userId)
        {
            var user = await _usersRepository.GetUserAsync(userId);

            return _mapper.Map<UserDTO>(user);
        }

        public async Task<UserDTO> UpdateUserAsync(UserDTO userDTO)
        {
            var user = await _usersRepository.UpdateUserAsync(_mapper.Map<User>(userDTO));

            return _mapper.Map<UserDTO>(user);
        }

        public async Task<UserDTO> AddUserAsync(UserDTO userDTO)
        {
            var user = _mapper.Map<User>(userDTO);

            var nameParts = user?.FirstName?.Split(' ') ?? Array.Empty<string>();

            if (nameParts.Length > 1)
                switch (nameParts.Length)
                {
                    case 1:
                        user.FirstName = nameParts[0];
                        break;
                    case 2:
                        user.FirstName = nameParts[0];
                        user.LastName = nameParts[1];
                        break;
                    case 3:
                        user.FirstName = nameParts[0];
                        user.SecondName = nameParts[1];
                        user.LastName = nameParts[2];
                        break;
                    default:
                        user.FirstName = nameParts[0];
                        user.SecondName = nameParts[1];
                        user.LastName = string.Join(" ", nameParts.Skip(2));
                        break;
                }

            return _mapper.Map<UserDTO>(await _usersRepository.AddUserAsync(user));
        }

        public async Task<List<CompanyNotificationSettingsDTO>> GetNoticationSettingsAsync(Guid userId)
        {
            var user = await _usersRepository.FindAsync(
                u => u.Id == userId,
                u => u.Include(u => u.Employees)
                      .ThenInclude(e => e.NotificationSettings)
                        .ThenInclude(ns => ns.NotificationType));

            var employees = user.SelectMany(u => u.Employees);

            var companyNotificataionSettings = new List<CompanyNotificationSettingsDTO>();

            foreach (var employee in employees)
            {
                companyNotificataionSettings.Add(new CompanyNotificationSettingsDTO
                {
                    CompanyId = employee.CompanyId,
                    NotificationSettings = _mapper.Map<List<NotificationSettingDTO>>(employee.NotificationSettings)
                });
            }

            return companyNotificataionSettings;
        }

        public async Task<IResult> UpdateUserEmailAsync(UserDTO userDTO)
        {
            var user = await _usersRepository.FirstOrDefaultAsync(u => u.Id == userDTO.Id);
            var hasEmailChanged = user.Email != userDTO.Email && user.Code.HasValue;
            if (user is null)
                return Results.NotFound();

            if (hasEmailChanged)
                user.Email = userDTO.Email;

            user.FirstName = userDTO.FirstName;
            user.SecondName = userDTO.SecondName;
            user.LastName = userDTO.LastName;

            await _usersRepository.UpdateUserAsync(user);

            if (hasEmailChanged)
            {
                var workTimeUrl = _configuration["GatewayUrl"];
                var confirmationUrl = $"{workTimeUrl}confirm-email?email={user.Email}&code={user.Code}";
                var emailPayload = new
                {
                    UserName = $"{user.FirstName} {user.LastName}".Trim(),
                    ConfirmationUrl = confirmationUrl,
                    user.Email
                };

                await _emailsNotificationService.SendEmailsAsync([userDTO.Email!], "email-added-to-user", emailPayload);
            }

            return Results.Ok(new { message = "Confirmation email sent" });
        }

        public async Task<IResult> ConfirmEmailCodeAsync(string email, string code)
        {
            var user = await _usersRepository.FirstOrDefaultAsync(u => u.Email == email && u.Code == int.Parse(code), u => u.Include(u => u.Employees));

            if (user is null)
                return Results.BadRequest("Invalid email");

            foreach (var employee in user.Employees)
            {
                if (string.IsNullOrEmpty(employee.WorkEmail))
                    employee.WorkEmail = email;
                if (string.IsNullOrEmpty(employee.Email))
                    employee.Email = email;
            }

            user.Code = null;

            await _usersRepository.UpdateUserAsync(user);

            return Results.Ok(_mapper.Map<UserDTO>(user));
        }
    }
}