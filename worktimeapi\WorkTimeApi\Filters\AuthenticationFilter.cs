﻿using Gateway.Common.Globals;
using Newtonsoft.Json;
using Serilog.Context;
using WorkTimeApi.Connections.Interfaces;
using WorkTimeApi.Services.Interfaces.Users;

namespace WorkTimeApi.Filters
{
    public class AuthenticationFilter(IHttpContextAccessor httpContextAccessor, ISSOConnection ssoConnection, IUserService userService) : IEndpointFilter
    {
        public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
        {
            if (httpContextAccessor.HttpContext is null)
                return TypedResults.Unauthorized();

            var authorizationHeader = httpContextAccessor.HttpContext.Request.Headers.Authorization.ToString();
            var refreshToken = httpContextAccessor.HttpContext.Request.Headers["refresh-token"].ToString();

            if (string.IsNullOrEmpty(authorizationHeader))
                return TypedResults.Unauthorized();

            var response = await ssoConnection.AuthenticateAsync(authorizationHeader, refreshToken);

            if (!response.IsSuccessStatusCode)
                return TypedResults.Unauthorized();

            if (!response.Headers.TryGetValues("Authorization", out var authorizations))
                return TypedResults.Unauthorized();

            if (!response.Headers.TryGetValues("refresh-token", out var refreshTokens))
                return TypedResults.Unauthorized();

            httpContextAccessor.HttpContext.Response.Headers.Authorization = authorizations.FirstOrDefault();
            httpContextAccessor.HttpContext.Response.Headers["refresh-token"] = refreshTokens.FirstOrDefault();

            var content = await response.Content.ReadAsStringAsync();
            var globalUser = JsonConvert.DeserializeObject<GlobalUser>(content);

            if (globalUser is null)
                return TypedResults.Unauthorized();

            httpContextAccessor.HttpContext.Request.Headers.Append("X-User-ID", globalUser.Id.ToString());

            var personalInformationDTO = await userService.GetUserAsync(globalUser.Id); ;

            var serviceProider = context.HttpContext.RequestServices;
            var _globalUser = serviceProider.GetService<GlobalUser>();

            _globalUser.Id = globalUser.Id;
            _globalUser.RefreshToken = refreshTokens.FirstOrDefault() ?? "";
            _globalUser.Email = globalUser.Email;
            _globalUser.FirstName = personalInformationDTO?.FirstName ?? "";
            _globalUser.SecondName = personalInformationDTO?.SecondName ?? "";
            _globalUser.LastName = personalInformationDTO?.LastName ?? "";

            using (LogContext.PushProperty("UserId", globalUser.Id))
            {
                return await next.Invoke(context);
            }
        }
    }
}
