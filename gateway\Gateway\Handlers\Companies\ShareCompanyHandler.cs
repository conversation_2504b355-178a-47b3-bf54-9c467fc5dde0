using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Requests.Companies;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Responses;

namespace Gateway.Handlers.Companies
{
    public class ShareCompanyHandler(IWorkTimeApiConnection workTimeApiConnection,
            IEmailsConnection emailsConnection,
            IConfiguration configuration,
            IUserRegistrationConnection userRegistrationConnection) : IRequestHandler<ShareCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(ShareCompanyRequest request, CancellationToken cancellationToken)
        {
            UserDTO? user = null;
            var userResponse = await userRegistrationConnection.GetSenderaUserAsync(request.Email ?? "");

            if (userResponse.StatusCode != System.Net.HttpStatusCode.NoContent)
            {
                user = await userResponse.Content.ReadFromJsonAsync<UserDTO>(cancellationToken);
                request.UserId = user.Id;
                request.User = user;
            }

            var response = await workTimeApiConnection.ShareCompanyAsync(request);

            var shareCompanyResponse = await response.Content.ReadFromJsonAsync<ShareCompanyResponse>();
            if (shareCompanyResponse is null)
                return Results.NoContent();

            if (shareCompanyResponse?.Employee?.Status != WorkTimeApi.Common.Enums.EmployeeStatus.Active)
                await emailsConnection.ShareCompanyAsync(new SendCompanyInvitationEmailRequest
                {
                    CompanyName = shareCompanyResponse.Company.Name,
                    Email = request.Email ?? throw new Exception("request.Email е празен"),
                    RoleName = request.RoleName,
                    Url = configuration["WorkTimeUrl"] ?? throw new Exception("WorkTimeUrl не е добавен в appsettings"),
                    IsUserRegistered = user != null,
                });

            return !response.IsSuccessStatusCode ? Results.StatusCode((int)response.StatusCode) : Results.Ok();
        }
    }
}