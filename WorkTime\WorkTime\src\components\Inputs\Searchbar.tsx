import React, { ChangeEvent, MouseEvent, useRef, useState } from "react";
import styled from "styled-components";
import searchIconHover from "../../assets/images/search/search-hover.svg";
import searchIcon from "../../assets/images/search/search.svg";
import Translator from "../../services/language/Translator";
import Container from "../Container";

interface SearchbarProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  label: string;
  placeholder: string;
  value?: string | number;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
  validation?: {
    isValid: boolean;
    alertMessage: string;
  };
  height?: number;
  direction?: "left" | "right";
}

const SearchBarContainer = styled(Container)<{ $height: number }>`
  position: relative;
  height: ${({ $height }) => `${$height}rem`};
  background-color: var(--searchbar-backround);
  border-radius: 2rem;
  display: block;
  text-align: start;
`;

const SearchButton = styled.button<{
  $isFocused: boolean;
  $direction: "left" | "right";
}>`
  position: relative;
  width: 1.7rem;
  height: 1.7rem;
  border: none;
  background-color: transparent;
  display: block;
  left: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  background-size: 1.7rem 1.7rem;
  transition: background-image 0.2s ease-in-out, left 0.2s ease-in-out;

  background-image: url(${searchIcon});

  &:hover {
    background-image: url(${searchIconHover});
  }
`;

const InputField = styled.input<{
  $isFocused: boolean;
  $direction: "left" | "right";
}>`
  position: absolute;
  border: 0px solid var(--textbox-border-color);
  border-radius: 2rem;
  font-size: 1rem;
  background: var(--textbox-color);
  outline: none;
  padding-left: ${({ $direction }) =>
    $direction === "left" ? "2.5rem" : "0.6rem"};
  top: 0.4rem;
  bottom: 0.4rem;
  left: 0.4rem;
  opacity: ${({ $isFocused }) => ($isFocused ? "1" : "0")};
  width: ${({ $isFocused, $direction }) =>
    $isFocused ? ($direction === "left" ? "62%" : "80%") : "50%"};
  transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out;
`;

const Searchbar = (props: SearchbarProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [isButtonFocused, setIsButtonFocused] = useState(false);
  const handleFocus = () => {
    setIsFocused(true);
    setIsButtonFocused(true);
  };

  const handleBlur = () => {
    if (value?.toString().trim() === "") {
      setIsFocused(false);
      setIsButtonFocused(false);
    }
  };

  const handleButtonClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    if (value?.toString().trim() === "") {
      setIsFocused(!isFocused);
    }

    if (!isFocused && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const {
    value,
    type,
    handleChange,
    onKeyDown,
    validation,
    name,
    height = 4,
    direction = "left",
  } = props;

  return (
    <SearchBarContainer $height={height} data-testid="searchbar-container">
      <InputField
        ref={searchInputRef}
        onFocus={handleFocus}
        type={type}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={onKeyDown}
        name={name}
        $isFocused={isFocused}
        $direction={direction}
        data-testid="searchbar-input"
      />
      <SearchButton
        $isFocused={isButtonFocused}
        $direction={direction}
        onClick={handleButtonClick}
        data-testid="searchbar-button"
      />
      {validation?.isValid === false && (
        <div data-testid="searchbar-validation-message">
          <Translator getString={validation.alertMessage} />
        </div>
      )}
    </SearchBarContainer>
  );
};

export default Searchbar;
